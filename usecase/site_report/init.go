package sitereport

import (
	basicpriceDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
	blockDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/block"
	consumptiontaxDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/consumption_tax"
	dailyreportadditionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/daily_report_addition"
	departmentpicDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/department_pic"
	distantfeeDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/distant_fee"
	incomeTaxDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/income_tax"
	optionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/option"
	qualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/qualification"
	sitereportDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report"
	sitereportdailyreportadditionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_daily_report_addition"
	sitereportoptionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_option"
	sitereportstatutoryDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_statutory"
	sitereportworkerDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_worker"
	sitereportworkerqualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_worker_qualification"
	statutoryDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/statutory"
	transferdestinationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/transfer_destination"
	userDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user"
	workerpayslipDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/worker_payslip"
	workerpayslipdetailDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/worker_payslip_detail"
)

type SiteReportUseCase struct {
	sitereport                    sitereportDmn.SiteReportDomainItf
	sitereportdailyreportaddition sitereportdailyreportadditionDmn.SiteReportDailyReportAdditionDomainItf
	sitereportoption              sitereportoptionDmn.SiteReportOptionDomainItf
	sitereportstatutory           sitereportstatutoryDmn.SiteReportStatutoryDomainItf
	basicPrice                    basicpriceDmn.BasicPriceDomainItf
	block                         blockDmn.BlockDomainItf
	statutory                     statutoryDmn.StatutoryDomainItf
	dailyReportAddition           dailyreportadditionDmn.DailyReportAdditionDomainItf
	departmentPic                 departmentpicDmn.DepartmentPicDomainItf
	distantfee                    distantfeeDmn.DistantFeeDomainItf
	incomeTax                     incomeTaxDmn.IncomeTaxDomainItf
	option                        optionDmn.OptionDomainItf
	qualification                 qualificationDmn.QualificationDomainItf
	sitereportworker              sitereportworkerDmn.SiteReportWorkerDomainItf
	sitereportworkerqualification sitereportworkerqualificationDmn.SiteReportWorkerQualificationDomainItf
	workerPayslip                 workerpayslipDmn.WorkerPayslipDomainItf
	workerPayslipDetail           workerpayslipdetailDmn.WorkerPayslipDetailDomainItf
	user                          userDmn.UserDomainItf
	consumptionTax                consumptiontaxDmn.ConsumptionTaxDomainItf
	transferDestination           transferdestinationDmn.TransferDestinationDomainItf
}

type Domains struct {
	SiteReportDomain                    sitereportDmn.SiteReportDomainItf
	SiteReportDailyReportAdditionDomain sitereportdailyreportadditionDmn.SiteReportDailyReportAdditionDomainItf
	SiteReportOptionDomain              sitereportoptionDmn.SiteReportOptionDomainItf
	SiteReportStatutoryDomain           sitereportstatutoryDmn.SiteReportStatutoryDomainItf
	BasicPriceDomain                    basicpriceDmn.BasicPriceDomainItf
	BlockDomain                         blockDmn.BlockDomainItf
	StatutoryDomain                     statutoryDmn.StatutoryDomainItf
	DailyReportAdditionDomain           dailyreportadditionDmn.DailyReportAdditionDomainItf
	DepartmentPicDomain                 departmentpicDmn.DepartmentPicDomainItf
	DistantFeeDomain                    distantfeeDmn.DistantFeeDomainItf
	IncomeTaxDomain                     incomeTaxDmn.IncomeTaxDomainItf
	OptionDomain                        optionDmn.OptionDomainItf
	QualificationDomain                 qualificationDmn.QualificationDomainItf
	SiteReportWorkerDomain              sitereportworkerDmn.SiteReportWorkerDomainItf
	SiteReportWorkerQualificationDomain sitereportworkerqualificationDmn.SiteReportWorkerQualificationDomainItf
	WorkerPayslipDomain                 workerpayslipDmn.WorkerPayslipDomainItf
	WorkerPayslipDetailDomain           workerpayslipdetailDmn.WorkerPayslipDetailDomainItf
	UserDomain                          userDmn.UserDomainItf
	ConsumptionTaxDomain                consumptiontaxDmn.ConsumptionTaxDomainItf
	TransferDestinationDomain           transferdestinationDmn.TransferDestinationDomainItf
}

func InitSiteReportUseCase(d Domains) *SiteReportUseCase {
	uc := &SiteReportUseCase{
		sitereport:                    d.SiteReportDomain,
		sitereportdailyreportaddition: d.SiteReportDailyReportAdditionDomain,
		sitereportoption:              d.SiteReportOptionDomain,
		sitereportstatutory:           d.SiteReportStatutoryDomain,
		basicPrice:                    d.BasicPriceDomain,
		block:                         d.BlockDomain,
		statutory:                     d.StatutoryDomain,
		dailyReportAddition:           d.DailyReportAdditionDomain,
		departmentPic:                 d.DepartmentPicDomain,
		distantfee:                    d.DistantFeeDomain,
		incomeTax:                     d.IncomeTaxDomain,
		option:                        d.OptionDomain,
		qualification:                 d.QualificationDomain,
		sitereportworker:              d.SiteReportWorkerDomain,
		sitereportworkerqualification: d.SiteReportWorkerQualificationDomain,
		workerPayslip:                 d.WorkerPayslipDomain,
		workerPayslipDetail:           d.WorkerPayslipDetailDomain,
		user:                          d.UserDomain,
		consumptionTax:                d.ConsumptionTaxDomain,
		transferDestination:           d.TransferDestinationDomain,
	}
	return uc
}
