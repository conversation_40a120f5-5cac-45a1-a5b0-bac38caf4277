package sitereport

import "errors"

var (
	ErrInvalidDateFormat        = errors.New("日付形式が無効です。YYYY-MM-DD形式で入力してください")
	ErrStartDateRequired        = errors.New("開始日は必須です")
	ErrEndDateRequired          = errors.New("終了日は必須です")
	ErrUserNotFound             = errors.New("ユーザーが見つかりません")
	ErrCannotUpdateLockedReport = errors.New("ロックされているレポートは更新できません")
)

const (
	LateEarlyBaseAmount = 1500
)

// Delivery slip
const (
	DefaultDeliverySlipAddress    = "〒488-0833尾張旭市東印場町二反田300番地\n株式会社 N B S\nTEL0561-53-5012 FAX0561-53-5013\n《担当者》草野 浩孝"
	DefaultDeliverySlipExpiration = "御見積後１ヶ月以内"

	UnitPiece  = "口"
	UnitTravel = "便"

	AddFeeLabel                = "加算プラン "
	DistrictBlockNameLabel     = "遠方交通費"
	DistrictBlockUnitLabel     = "ユニット遠方交通費"
	TransitPlaceBlockNameLabel = "遠方交通費(経由)"
	TransitPlaceBlockUnitLabel = "ユニット遠方交通費(経由)"
	LateEarlyLabel             = "深夜/早朝"
	ExtraTimeChargeLabel       = "延長請求"
)

// Invoice
const (
	DefaultInvoiceAddress = "株式会社NBS\n\n〒488-0833\n尾張旭市東印場町二反田300番地\n\nTEL (0561)53-5012  FAX (0561)53-5013\n登録番号: T6180001073253"
	InvoiceQuantityUnit   = "一式"
)

// Statutory
const (
	DefaultStatutoryAddress = "株式会社NBS\n〒488-0833\n尾張旭市東印場町二反田300番地\nTEL (0561)53-5012 FAX (0561)53-5013"
)

// Payslip
const (
	DefaultPayslipAddress = "株式会社NBS\n尾張旭市東印場町二反田300番地\nTEL  (0561)53-5012  FAX  (0561)53-5013"
	DefaultPayslipStatus  = "済"
)
