package migration

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// DistantTravelAllowance represents the distant travel allowance table structure
type DistantTravelAllowance struct {
	ID          int64      `gorm:"column:id;primary_key"`
	Code        string     `gorm:"column:code;unique"`
	Title       string     `gorm:"column:title"`
	Explanation string     `gorm:"column:explanation"`
	AddClaim    float64    `gorm:"column:add_claim"`
	AddPaid     float64    `gorm:"column:add_paid"`
	CreatedAt   time.Time  `gorm:"column:created_at"`
	UpdatedAt   time.Time  `gorm:"column:updated_at"`
	DeletedAt   *time.Time `gorm:"column:deleted_at"`
}

// TableName returns the table name for GORM
func (DistantTravelAllowance) TableName() string {
	return "distant_travel_allowance"
}

// DistantTravelAllowanceParser handles parsing of 遠方出張諸手当マスタ file
type DistantTravelAllowanceParser struct {
	BaseParser
}

// NewDistantTravelAllowanceParser creates a new distant travel allowance parser
func NewDistantTravelAllowanceParser() *DistantTravelAllowanceParser {
	return &DistantTravelAllowanceParser{
		BaseParser: BaseParser{tableName: "distant_travel_allowance"},
	}
}

// Parse parses the distant travel allowance master file
func (p *DistantTravelAllowanceParser) Parse(filePath string) ([]map[string]string, error) {
	return p.ParseLegacyFile(filePath)
}

// Validate validates a distant travel allowance record
func (p *DistantTravelAllowanceParser) Validate(record map[string]string) error {
	requiredFields := []string{"FLD_Block"}
	return ValidateRequired(record, requiredFields)
}

// DistantTravelAllowanceMigrator handles migration of distant travel allowance data
type DistantTravelAllowanceMigrator struct {
	// No domain interface needed for this simple migration
}

// NewDistantTravelAllowanceMigrator creates a new distant travel allowance migrator
func NewDistantTravelAllowanceMigrator() *DistantTravelAllowanceMigrator {
	return &DistantTravelAllowanceMigrator{}
}

// GetDependencies returns the list of tables this migrator depends on
func (m *DistantTravelAllowanceMigrator) GetDependencies() []string {
	return []string{} // No dependencies
}

// Migrate migrates distant travel allowance records to the database
func (m *DistantTravelAllowanceMigrator) Migrate(ctx context.Context, tx *gorm.DB, records []map[string]string, dryRun bool) (*MigrationResult, error) {
	result := &MigrationResult{
		RecordsInserted: 0,
		Errors:          []string{},
	}

	// Track used codes to ensure uniqueness
	usedCodes := make(map[string]int)

	for i, record := range records {
		// Extract and transform data
		blockCode := record["FLD_Block"]
		if blockCode == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing block code", i+1))
			continue
		}

		// Use block code as the base for our code
		baseCode := fmt.Sprintf("travel_allowance_%s", blockCode)
		code := baseCode
		if count, exists := usedCodes[baseCode]; exists {
			code = fmt.Sprintf("%s_%d", baseCode, count+1)
		}
		usedCodes[baseCode]++

		// Calculate total add_claim and add_paid from EmKinM01-08 fields
		var totalAddClaim, totalAddPaid float64

		// Sum all EmKinM fields (EmKinM01 through EmKinM08)
		for j := 1; j <= 8; j++ {
			fieldName := fmt.Sprintf("EmKinM%02d", j)
			if value, exists := record[fieldName]; exists && value != "" {
				if amount, err := ParseFloat(value); err == nil {
					totalAddClaim += amount
					totalAddPaid += amount // Same value for both as per analysis
				}
			}
		}

		// Parse dates for created_at and updated_at
		var createdAt, updatedAt time.Time
		if createDateStr := record["FLD_CreateDate"]; createDateStr != "" {
			if parsedDate, err := ParseDate(createDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					createdAt = t
				}
			}
		}
		if createdAt.IsZero() {
			createdAt = time.Now()
		}

		if updateDateStr := record["FLD_UpdateDate"]; updateDateStr != "" {
			if parsedDate, err := ParseDate(updateDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					updatedAt = t
				}
			}
		}
		if updatedAt.IsZero() {
			updatedAt = createdAt
		}

		// Create distant travel allowance record
		distantTravelAllowanceRecord := DistantTravelAllowance{
			Code:        code,
			Title:       fmt.Sprintf("Travel Allowance for Block %s", blockCode),
			Explanation: fmt.Sprintf("Travel allowance configuration for block %s", blockCode),
			AddClaim:    totalAddClaim,
			AddPaid:     totalAddPaid,
			CreatedAt:   createdAt,
			UpdatedAt:   updatedAt,
			DeletedAt:   nil,
		}

		// Insert record (skip in dry run mode)
		if !dryRun {
			err := tx.WithContext(ctx).Create(&distantTravelAllowanceRecord).Error
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Record %d: database error: %v", i+1, err))
				continue
			}
		}

		result.RecordsInserted++
	}

	return result, nil
}

// CheckExistingDistantTravelAllowances checks if distant travel allowance records already exist in the database
func (m *DistantTravelAllowanceMigrator) CheckExistingDistantTravelAllowances(ctx context.Context, tx *gorm.DB) (map[string]bool, error) {
	var existingRecords []DistantTravelAllowance
	err := tx.WithContext(ctx).Select("code").Find(&existingRecords).Error
	if err != nil {
		return nil, fmt.Errorf("failed to check existing distant travel allowance records: %w", err)
	}

	existing := make(map[string]bool)
	for _, record := range existingRecords {
		existing[record.Code] = true
	}

	return existing, nil
}

// PreMigrationValidation performs validation before migration
func (m *DistantTravelAllowanceMigrator) PreMigrationValidation(ctx context.Context, tx *gorm.DB, records []map[string]string) error {
	// Check if table exists
	if !tx.Migrator().HasTable(&DistantTravelAllowance{}) {
		return fmt.Errorf("distant_travel_allowance table does not exist - please run database migrations first")
	}

	// Check for duplicate block codes in the input data
	blocks := make(map[string]int)
	for i, record := range records {
		block := record["FLD_Block"]
		if block == "" {
			continue
		}

		if existingIndex, exists := blocks[block]; exists {
			return fmt.Errorf("duplicate block code '%s' found in records %d and %d", block, existingIndex+1, i+1)
		}
		blocks[block] = i
	}

	return nil
}

// PostMigrationValidation performs validation after migration
func (m *DistantTravelAllowanceMigrator) PostMigrationValidation(ctx context.Context, tx *gorm.DB, expectedCount int) error {
	var count int64
	err := tx.WithContext(ctx).Model(&DistantTravelAllowance{}).Where("deleted_at IS NULL").Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to count distant travel allowance records: %w", err)
	}

	if int(count) < expectedCount {
		return fmt.Errorf("expected at least %d distant travel allowance records, but found %d", expectedCount, count)
	}

	return nil
}
