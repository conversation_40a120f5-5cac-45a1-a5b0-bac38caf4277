package migration

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	income_tax "github.com/Sera-Global/be-nbs-accounting-system/domain/income_tax"
	"gorm.io/gorm"
)

// IncomeTaxParser handles parsing of 所得税税額マスタ file
type IncomeTaxParser struct {
	BaseParser
}

// NewIncomeTaxParser creates a new income tax parser
func NewIncomeTaxParser() *IncomeTaxParser {
	return &IncomeTaxParser{
		BaseParser: BaseParser{tableName: "income_tax"},
	}
}

// Parse parses the income tax master file
func (p *IncomeTaxParser) Parse(filePath string) ([]map[string]string, error) {
	return p.ParseLegacyFile(filePath)
}

// Validate validates an income tax record
func (p *IncomeTaxParser) Validate(record map[string]string) error {
	requiredFields := []string{"m01", "m02", "mZeiGaku"}
	return ValidateRequired(record, requiredFields)
}

// IncomeTaxMigrator handles migration of income tax data
type IncomeTaxMigrator struct {
	incomeTaxDomain income_tax.IncomeTaxDomainItf
}

// NewIncomeTaxMigrator creates a new income tax migrator
func NewIncomeTaxMigrator() *IncomeTaxMigrator {
	// Initialize income tax domain
	incomeTaxResource := income_tax.IncomeTaxResource{}
	incomeTaxDomain := income_tax.InitIncomeTaxDomain(incomeTaxResource)

	return &IncomeTaxMigrator{
		incomeTaxDomain: &incomeTaxDomain,
	}
}

// GetDependencies returns the list of tables this migrator depends on
func (m *IncomeTaxMigrator) GetDependencies() []string {
	return []string{} // No dependencies
}

// Migrate migrates income tax records to the database
func (m *IncomeTaxMigrator) Migrate(ctx context.Context, tx *gorm.DB, records []map[string]string, dryRun bool) (*MigrationResult, error) {
	result := &MigrationResult{
		RecordsInserted: 0,
		Errors:          []string{},
	}

	// Track used codes to ensure uniqueness
	usedCodes := make(map[string]int)

	for i, record := range records {
		// Extract and transform data
		moreThan, err := ParseFloat(record["m01"])
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: invalid more_than value: %v", i+1, err))
			continue
		}

		lessThan, err := ParseFloat(record["m02"])
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: invalid less_than value: %v", i+1, err))
			continue
		}

		taxAmount, err := ParseFloat(record["mZeiGaku"])
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: invalid tax amount: %v", i+1, err))
			continue
		}

		// Generate unique code from range
		baseCode := fmt.Sprintf("tax_%.0f_%.0f", moreThan, lessThan)
		code := baseCode
		if count, exists := usedCodes[baseCode]; exists {
			code = fmt.Sprintf("%s_%d", baseCode, count+1)
		}
		usedCodes[baseCode]++

		// Create amount JSON structure
		amountData := []income_tax.TaxAmountItem{
			{
				Type:  constanta.TaxDependentSingle,
				Price: taxAmount,
			},
			{
				Type:  constanta.TaxDependent0,
				Price: taxAmount,
			},
			{
				Type:  constanta.TaxDependent1,
				Price: taxAmount,
			},
			{
				Type:  constanta.TaxDependent2,
				Price: taxAmount,
			},
			{
				Type:  constanta.TaxDependent3,
				Price: taxAmount,
			},
			{
				Type:  constanta.TaxDependent4,
				Price: taxAmount,
			},
			{
				Type:  constanta.TaxDependent5,
				Price: taxAmount,
			},
			{
				Type:  constanta.TaxDependent6,
				Price: taxAmount,
			},
			{
				Type:  constanta.TaxDependent7,
				Price: taxAmount,
			},
		}

		amountJSON, err := json.Marshal(amountData)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: failed to create amount JSON: %v", i+1, err))
			continue
		}

		// Parse dates for created_at and updated_at
		var createdAt, updatedAt time.Time
		if createDateStr := record["FLD_CreateDate"]; createDateStr != "" {
			if parsedDate, err := ParseDate(createDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					createdAt = t
				}
			}
		}
		if createdAt.IsZero() {
			createdAt = time.Now()
		}

		if updateDateStr := record["FLD_UpdateDate"]; updateDateStr != "" {
			if parsedDate, err := ParseDate(updateDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					updatedAt = t
				}
			}
		}
		if updatedAt.IsZero() {
			updatedAt = createdAt
		}

		// Create income tax record
		incomeTaxRecord := income_tax.IncomeTax{
			Code:      code,
			MoreThan:  moreThan,
			LessThan:  lessThan,
			Amount:    string(amountJSON),
			CreatedAt: createdAt,
			UpdatedAt: updatedAt,
			DeletedAt: nil,
		}

		// Insert record (skip in dry run mode)
		if !dryRun {
			err := tx.WithContext(ctx).Create(&incomeTaxRecord).Error
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Record %d: database error: %v", i+1, err))
				continue
			}
		}

		result.RecordsInserted++
	}

	return result, nil
}

// CheckExistingIncomeTax checks if income tax records already exist in the database
func (m *IncomeTaxMigrator) CheckExistingIncomeTax(ctx context.Context, tx *gorm.DB) (map[string]bool, error) {
	var existingRecords []income_tax.IncomeTax
	err := tx.WithContext(ctx).Select("code").Find(&existingRecords).Error
	if err != nil {
		return nil, fmt.Errorf("failed to check existing income tax records: %w", err)
	}

	existing := make(map[string]bool)
	for _, record := range existingRecords {
		existing[record.Code] = true
	}

	return existing, nil
}

// PreMigrationValidation performs validation before migration
func (m *IncomeTaxMigrator) PreMigrationValidation(ctx context.Context, tx *gorm.DB, records []map[string]string) error {
	// Check if table exists
	if !tx.Migrator().HasTable(&income_tax.IncomeTax{}) {
		return fmt.Errorf("income_tax table does not exist - please run database migrations first")
	}

	// Check for overlapping ranges in the input data
	ranges := make([]struct {
		moreThan float64
		lessThan float64
		index    int
	}, 0, len(records))

	for i, record := range records {
		moreThan, err := ParseFloat(record["m01"])
		if err != nil {
			continue
		}
		lessThan, err := ParseFloat(record["m02"])
		if err != nil {
			continue
		}

		// Check for overlaps with existing ranges
		for _, existing := range ranges {
			if moreThan < existing.lessThan && lessThan > existing.moreThan {
				return fmt.Errorf("overlapping tax ranges found: records %d (%.0f-%.0f) and %d (%.0f-%.0f)",
					existing.index+1, existing.moreThan, existing.lessThan,
					i+1, moreThan, lessThan)
			}
		}

		ranges = append(ranges, struct {
			moreThan float64
			lessThan float64
			index    int
		}{moreThan, lessThan, i})
	}

	return nil
}

// PostMigrationValidation performs validation after migration
func (m *IncomeTaxMigrator) PostMigrationValidation(ctx context.Context, tx *gorm.DB, expectedCount int) error {
	var count int64
	err := tx.WithContext(ctx).Model(&income_tax.IncomeTax{}).Where("deleted_at IS NULL").Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to count income tax records: %w", err)
	}

	if int(count) < expectedCount {
		return fmt.Errorf("expected at least %d income tax records, but found %d", expectedCount, count)
	}

	return nil
}
