package migration

import (
	"context"
	"fmt"
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/domain/qualification"
	"gorm.io/gorm"
)

// QualificationParser handles parsing of 資格マスタ file
type QualificationParser struct {
	BaseParser
}

// NewQualificationParser creates a new qualification parser
func NewQualificationParser() *QualificationParser {
	return &QualificationParser{
		BaseParser: BaseParser{tableName: "qualification"},
	}
}

// Parse parses the qualification master file
func (p *QualificationParser) Parse(filePath string) ([]map[string]string, error) {
	return p.ParseLegacyFile(filePath)
}

// Validate validates a qualification record
func (p *QualificationParser) Validate(record map[string]string) error {
	requiredFields := []string{"FLD_SikakuName"}
	return ValidateRequired(record, requiredFields)
}

// QualificationMigrator handles migration of qualification data
type QualificationMigrator struct {
	qualificationDomain qualification.QualificationDomainItf
}

// NewQualificationMigrator creates a new qualification migrator
func NewQualificationMigrator() *QualificationMigrator {
	// Initialize qualification domain
	qualificationResource := qualification.QualificationResource{}
	qualificationDomain := qualification.InitQualificationDomain(qualificationResource)

	return &QualificationMigrator{
		qualificationDomain: &qualificationDomain,
	}
}

// GetDependencies returns the list of tables this migrator depends on
func (m *QualificationMigrator) GetDependencies() []string {
	return []string{} // No dependencies
}

// Migrate migrates qualification records to the database
func (m *QualificationMigrator) Migrate(ctx context.Context, tx *gorm.DB, records []map[string]string, dryRun bool) (*MigrationResult, error) {
	result := &MigrationResult{
		RecordsInserted: 0,
		Errors:         []string{},
	}

	// Track used codes to ensure uniqueness
	usedCodes := make(map[string]int)

	for i, record := range records {
		// Extract and transform data
		title := record["FLD_SikakuName"]
		if title == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing title", i+1))
			continue
		}

		// Generate unique code
		baseCode := GenerateCode(title)
		code := baseCode
		if count, exists := usedCodes[baseCode]; exists {
			code = fmt.Sprintf("%s_%d", baseCode, count+1)
		}
		usedCodes[baseCode]++

		// Parse amounts
		addClaim, err := ParseFloat(record["FLD_Kingaku"])
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: invalid add_claim: %v", i+1, err))
			continue
		}

		paidAmount, err := ParseFloat(record["FLD_SH_Kingaku"])
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: invalid paid_amount: %v", i+1, err))
			continue
		}

		// Parse dates
		var createdAt, updatedAt time.Time
		if createDateStr := record["FLD_CreateDate"]; createDateStr != "" {
			if parsedDate, err := ParseDate(createDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					createdAt = t
				}
			}
		}
		if createdAt.IsZero() {
			createdAt = time.Now()
		}

		if updateDateStr := record["FLD_UpdateDate"]; updateDateStr != "" {
			if parsedDate, err := ParseDate(updateDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					updatedAt = t
				}
			}
		}
		if updatedAt.IsZero() {
			updatedAt = createdAt
		}

		// Create qualification record
		qualificationRecord := qualification.Qualification{
			Code:        code,
			Title:       title,
			Explanation: "", // Empty as per analysis
			AddClaim:    addClaim,
			PaidAmount:  paidAmount,
			CreatedAt:   createdAt,
			UpdatedAt:   updatedAt,
			DeletedAt:   nil,
		}

		// Insert record (skip in dry run mode)
		if !dryRun {
			err := tx.WithContext(ctx).Create(&qualificationRecord).Error
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Record %d: database error: %v", i+1, err))
				continue
			}
		}

		result.RecordsInserted++
	}

	return result, nil
}

// QualificationCreateParam represents parameters for creating qualification
type QualificationCreateParam struct {
	Code        string
	Title       string
	Explanation string
	AddClaim    float64
	PaidAmount  float64
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

// Additional helper functions for qualification migration

// CheckExistingQualifications checks if qualifications already exist in the database
func (m *QualificationMigrator) CheckExistingQualifications(ctx context.Context, tx *gorm.DB) (map[string]bool, error) {
	var existingQualifications []qualification.Qualification
	err := tx.WithContext(ctx).Select("code").Find(&existingQualifications).Error
	if err != nil {
		return nil, fmt.Errorf("failed to check existing qualifications: %w", err)
	}

	existing := make(map[string]bool)
	for _, qual := range existingQualifications {
		existing[qual.Code] = true
	}

	return existing, nil
}

// PreMigrationValidation performs validation before migration
func (m *QualificationMigrator) PreMigrationValidation(ctx context.Context, tx *gorm.DB, records []map[string]string) error {
	// Check if table exists
	if !tx.Migrator().HasTable(&qualification.Qualification{}) {
		return fmt.Errorf("qualification table does not exist - please run database migrations first")
	}

	// Check for duplicate titles in the input data
	titles := make(map[string]int)
	for i, record := range records {
		title := record["FLD_SikakuName"]
		if title == "" {
			continue
		}
		if existingIndex, exists := titles[title]; exists {
			return fmt.Errorf("duplicate title '%s' found in records %d and %d", title, existingIndex+1, i+1)
		}
		titles[title] = i
	}

	return nil
}

// PostMigrationValidation performs validation after migration
func (m *QualificationMigrator) PostMigrationValidation(ctx context.Context, tx *gorm.DB, expectedCount int) error {
	var count int64
	err := tx.WithContext(ctx).Model(&qualification.Qualification{}).Where("deleted_at IS NULL").Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to count qualifications: %w", err)
	}

	if int(count) < expectedCount {
		return fmt.Errorf("expected at least %d qualifications, but found %d", expectedCount, count)
	}

	return nil
}
