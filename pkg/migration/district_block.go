package migration

import (
	"context"
	"fmt"
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/domain/block"
	"github.com/Sera-Global/be-nbs-accounting-system/domain/district"
	"gorm.io/gorm"
)

// DistrictBlockParser handles parsing of 地区マスタ file
type DistrictBlockParser struct {
	BaseParser
}

// NewDistrictBlockParser creates a new district/block parser
func NewDistrictBlockParser() *DistrictBlockParser {
	return &DistrictBlockParser{
		BaseParser: BaseParser{tableName: "district"},
	}
}

// Parse parses the district master file
func (p *DistrictBlockParser) Parse(filePath string) ([]map[string]string, error) {
	return p.ParseLegacyFile(filePath)
}

// Validate validates a district/block record
func (p *DistrictBlockParser) Validate(record map[string]string) error {
	requiredFields := []string{"FLD_TikuName", "FLD_Block"}
	return ValidateRequired(record, requiredFields)
}

// DistrictBlockMigrator handles migration of district and block data
type DistrictBlockMigrator struct {
	blockDomain    block.BlockDomainItf
	districtDomain district.DistrictDomainItf
}

// NewDistrictBlockMigrator creates a new district/block migrator
func NewDistrictBlockMigrator() *DistrictBlockMigrator {
	// Initialize domains
	blockResource := block.BlockResource{}
	blockDomain := block.InitBlockDomain(blockResource)

	districtResource := district.DistrictResource{}
	districtDomain := district.InitDistrictDomain(districtResource)

	return &DistrictBlockMigrator{
		blockDomain:    &blockDomain,
		districtDomain: &districtDomain,
	}
}

// GetDependencies returns the list of tables this migrator depends on
func (m *DistrictBlockMigrator) GetDependencies() []string {
	return []string{} // No dependencies
}

// Migrate migrates district and block records to the database
func (m *DistrictBlockMigrator) Migrate(ctx context.Context, tx *gorm.DB, records []map[string]string, dryRun bool) (*MigrationResult, error) {
	result := &MigrationResult{
		RecordsInserted: 0,
		Errors:          []string{},
	}

	// Track blocks and districts to avoid duplicates
	createdBlocks := make(map[string]int64)
	usedDistrictCodes := make(map[string]int)

	for i, record := range records {
		// Extract data
		districtName := record["FLD_TikuName"]
		blockCode := record["FLD_Block"]

		if districtName == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing district name", i+1))
			continue
		}

		if blockCode == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing block code", i+1))
			continue
		}

		// Set timestamps
		var createdAt, updatedAt time.Time
		if createDateStr := record["FLD_CreateDate"]; createDateStr != "" {
			if parsedDate, err := ParseDate(createDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					createdAt = t
				}
			}
		}
		if createdAt.IsZero() {
			createdAt = time.Now()
		}

		if updateDateStr := record["FLD_UpdateDate"]; updateDateStr != "" {
			if parsedDate, err := ParseDate(updateDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					updatedAt = t
				}
			}
		}
		if updatedAt.IsZero() {
			updatedAt = createdAt
		}

		// Create or get block
		var blockID int64
		if existingBlockID, exists := createdBlocks[blockCode]; exists {
			blockID = existingBlockID
		} else {
			// Create new block
			blockRecord := block.Block{
				Code:           blockCode,
				Name:           fmt.Sprintf("Block %s", blockCode),
				UnitPrice:      0, // Default value
				PricePerWorker: 0, // Default value
				CreatedAt:      createdAt,
				UpdatedAt:      updatedAt,
				DeletedAt:      nil,
			}

			if !dryRun {
				err := tx.WithContext(ctx).Create(&blockRecord).Error
				if err != nil {
					result.Errors = append(result.Errors, fmt.Sprintf("Record %d: failed to create block: %v", i+1, err))
					continue
				}
				blockID = blockRecord.ID
			} else {
				blockID = int64(len(createdBlocks) + 1) // Mock ID for dry run
			}

			createdBlocks[blockCode] = blockID
		}

		// Generate unique district code
		baseDistrictCode := GenerateCode(districtName)
		districtCode := baseDistrictCode
		if count, exists := usedDistrictCodes[baseDistrictCode]; exists {
			districtCode = fmt.Sprintf("%s_%d", baseDistrictCode, count+1)
		}
		usedDistrictCodes[baseDistrictCode]++

		// Create district record
		districtRecord := district.District{
			BlockID:   blockID,
			Code:      districtCode,
			Name:      districtName,
			CreatedAt: createdAt,
			UpdatedAt: updatedAt,
			DeletedAt: nil,
		}

		// Insert district record (skip in dry run mode)
		if !dryRun {
			err := tx.WithContext(ctx).Create(&districtRecord).Error
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Record %d: database error: %v", i+1, err))
				continue
			}
		}

		result.RecordsInserted++
	}

	return result, nil
}

// CheckExistingDistrictsAndBlocks checks if districts and blocks already exist in the database
func (m *DistrictBlockMigrator) CheckExistingDistrictsAndBlocks(ctx context.Context, tx *gorm.DB) (map[string]bool, map[string]bool, error) {
	// Check existing blocks
	var existingBlocks []block.Block
	err := tx.WithContext(ctx).Select("code").Find(&existingBlocks).Error
	if err != nil {
		return nil, nil, fmt.Errorf("failed to check existing blocks: %w", err)
	}

	existingBlockCodes := make(map[string]bool)
	for _, b := range existingBlocks {
		existingBlockCodes[b.Code] = true
	}

	// Check existing districts
	var existingDistricts []district.District
	err = tx.WithContext(ctx).Select("code").Find(&existingDistricts).Error
	if err != nil {
		return nil, nil, fmt.Errorf("failed to check existing districts: %w", err)
	}

	existingDistrictCodes := make(map[string]bool)
	for _, d := range existingDistricts {
		existingDistrictCodes[d.Code] = true
	}

	return existingBlockCodes, existingDistrictCodes, nil
}

// PreMigrationValidation performs validation before migration
func (m *DistrictBlockMigrator) PreMigrationValidation(ctx context.Context, tx *gorm.DB, records []map[string]string) error {
	// Check if tables exist
	if !tx.Migrator().HasTable(&block.Block{}) {
		return fmt.Errorf("block table does not exist - please run database migrations first")
	}

	if !tx.Migrator().HasTable(&district.District{}) {
		return fmt.Errorf("district table does not exist - please run database migrations first")
	}

	// Check for duplicate district names in the input data
	names := make(map[string]int)
	for i, record := range records {
		name := record["FLD_TikuName"]
		if name == "" {
			continue
		}
		if existingIndex, exists := names[name]; exists {
			return fmt.Errorf("duplicate district name '%s' found in records %d and %d", name, existingIndex+1, i+1)
		}
		names[name] = i
	}

	return nil
}

// PostMigrationValidation performs validation after migration
func (m *DistrictBlockMigrator) PostMigrationValidation(ctx context.Context, tx *gorm.DB, expectedCount int) error {
	// Check district count
	var districtCount int64
	err := tx.WithContext(ctx).Model(&district.District{}).Where("deleted_at IS NULL").Count(&districtCount).Error
	if err != nil {
		return fmt.Errorf("failed to count districts: %w", err)
	}

	if int(districtCount) < expectedCount {
		return fmt.Errorf("expected at least %d districts, but found %d", expectedCount, districtCount)
	}

	// Check block count (should be at least 1)
	var blockCount int64
	err = tx.WithContext(ctx).Model(&block.Block{}).Where("deleted_at IS NULL").Count(&blockCount).Error
	if err != nil {
		return fmt.Errorf("failed to count blocks: %w", err)
	}

	if blockCount == 0 {
		return fmt.Errorf("no blocks were created")
	}

	return nil
}
