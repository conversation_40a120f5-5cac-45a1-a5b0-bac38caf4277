package migration

import (
	"bufio"
	"fmt"
	"os"
	"regexp"
	"strings"

	"golang.org/x/text/encoding/japanese"
	"golang.org/x/text/transform"
)

// BaseParser provides common functionality for parsing legacy data files
type BaseParser struct {
	tableName string
}

// ParseLegacyFile parses a legacy data file with the standard format
func (p *BaseParser) ParseLegacyFile(filePath string) ([]map[string]string, error) {
	// Open file
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// Create decoder for Japanese text (try Shift-JIS first)
	decoder := japanese.ShiftJIS.NewDecoder()
	reader := transform.NewReader(file, decoder)
	scanner := bufio.NewScanner(reader)

	var records []map[string]string
	currentRecord := make(map[string]string)
	fieldPattern := regexp.MustCompile(`^([^:]+):\s*(.*)$`)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// Skip empty lines - they separate records
		if line == "" {
			if len(currentRecord) > 0 {
				// Filter out metadata fields
				cleanRecord := p.filterMetadataFields(currentRecord)
				if len(cleanRecord) > 0 {
					records = append(records, cleanRecord)
				}
				currentRecord = make(map[string]string)
			}
			continue
		}

		// Parse field-value pairs
		matches := fieldPattern.FindStringSubmatch(line)
		if len(matches) == 3 {
			fieldName := strings.TrimSpace(matches[1])
			fieldValue := strings.TrimSpace(matches[2])
			currentRecord[fieldName] = fieldValue
		}
	}

	// Don't forget the last record
	if len(currentRecord) > 0 {
		cleanRecord := p.filterMetadataFields(currentRecord)
		if len(cleanRecord) > 0 {
			records = append(records, cleanRecord)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading file: %w", err)
	}

	return records, nil
}

// filterMetadataFields removes metadata fields that we don't need for migration
func (p *BaseParser) filterMetadataFields(record map[string]string) map[string]string {
	metadataFields := map[string]bool{
		"$UpdatedBy":       true,
		"$Revisions":       true,
		"FLD_CreateDate":   false, // Keep this for created_at
		"FLD_LastUpdate":   true,
		"FLD_UpdateDate":   false, // Keep this for updated_at
	}

	cleanRecord := make(map[string]string)
	for key, value := range record {
		if exclude, exists := metadataFields[key]; !exists || !exclude {
			cleanRecord[key] = value
		}
	}

	return cleanRecord
}

// GetTableName returns the target table name
func (p *BaseParser) GetTableName() string {
	return p.tableName
}

// ParseDate parses a date string in YYYY/MM/DD format
func ParseDate(dateStr string) (string, error) {
	if dateStr == "" {
		return "", nil
	}

	// Handle YYYY/MM/DD format
	parts := strings.Split(dateStr, "/")
	if len(parts) != 3 {
		return "", fmt.Errorf("invalid date format: %s", dateStr)
	}

	// Return in ISO format for PostgreSQL
	return fmt.Sprintf("%s-%s-%s", parts[0], parts[1], parts[2]), nil
}

// ParseFloat parses a float value, handling empty strings and Japanese number formats
func ParseFloat(floatStr string) (float64, error) {
	if floatStr == "" {
		return 0, nil
	}

	// Remove any whitespace
	floatStr = strings.TrimSpace(floatStr)
	
	// Handle empty string after trimming
	if floatStr == "" {
		return 0, nil
	}

	// Try to parse as float
	var result float64
	_, err := fmt.Sscanf(floatStr, "%f", &result)
	if err != nil {
		return 0, fmt.Errorf("invalid float format: %s", floatStr)
	}

	return result, nil
}

// GenerateCode generates a unique code from a title/name
func GenerateCode(title string) string {
	if title == "" {
		return ""
	}

	// Convert to lowercase
	code := strings.ToLower(title)
	
	// Replace spaces and special characters with underscores
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	code = reg.ReplaceAllString(code, "_")
	
	// Remove leading/trailing underscores
	code = strings.Trim(code, "_")
	
	// Limit length
	if len(code) > 50 {
		code = code[:50]
	}

	return code
}

// ValidateRequired checks if required fields are present and not empty
func ValidateRequired(record map[string]string, requiredFields []string) error {
	for _, field := range requiredFields {
		value, exists := record[field]
		if !exists || strings.TrimSpace(value) == "" {
			return fmt.Errorf("required field '%s' is missing or empty", field)
		}
	}
	return nil
}
