package migration

import (
	"context"
	"fmt"
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/domain/user"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// WorkerMasterParser handles parsing of 作業員マスタ file
type WorkerMasterParser struct {
	BaseParser
}

// NewWorkerMasterParser creates a new worker master parser
func NewWorkerMasterParser() *WorkerMasterParser {
	return &WorkerMasterParser{
		BaseParser: BaseParser{tableName: "user"},
	}
}

// Parse parses the worker master file
func (p *WorkerMasterParser) Parse(filePath string) ([]map[string]string, error) {
	return p.ParseLegacyFile(filePath)
}

// Validate validates a worker master record
func (p *WorkerMasterParser) Validate(record map[string]string) error {
	requiredFields := []string{"FLD_S_CODE", "FLD_S_NAME"}
	return ValidateRequired(record, requiredFields)
}

// WorkerMasterMigrator handles migration of worker master data
type WorkerMasterMigrator struct {
	userDomain user.UserDomainItf
}

// NewWorkerMasterMigrator creates a new worker master migrator
func NewWorkerMasterMigrator() *WorkerMasterMigrator {
	// Initialize user domain
	userResource := user.UserResource{}
	userDomain := user.InitUserDomain(userResource)

	return &WorkerMasterMigrator{
		userDomain: &userDomain,
	}
}

// GetDependencies returns the list of tables this migrator depends on
func (m *WorkerMasterMigrator) GetDependencies() []string {
	return []string{} // No dependencies
}

// Migrate migrates worker master records to the database
func (m *WorkerMasterMigrator) Migrate(ctx context.Context, tx *gorm.DB, records []map[string]string, dryRun bool) (*MigrationResult, error) {
	result := &MigrationResult{
		RecordsInserted: 0,
		Errors:          []string{},
	}

	// Track used usernames to ensure uniqueness
	usedUsernames := make(map[string]int)

	for i, record := range records {
		// Extract and transform data
		username := record["FLD_S_CODE"]
		name := record["FLD_S_NAME"]

		if username == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing worker code", i+1))
			continue
		}

		if name == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing worker name", i+1))
			continue
		}

		// Ensure username uniqueness
		baseUsername := username
		if count, exists := usedUsernames[baseUsername]; exists {
			username = fmt.Sprintf("%s_%d", baseUsername, count+1)
		}
		usedUsernames[baseUsername]++

		// Generate default password hash
		defaultPassword := "worker123"
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(defaultPassword), bcrypt.DefaultCost)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: failed to hash password: %v", i+1, err))
			continue
		}

		// Parse dates for created_at and updated_at
		var createdAt, updatedAt time.Time
		if createDateStr := record["FLD_CreateDate"]; createDateStr != "" {
			if parsedDate, err := ParseDate(createDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					createdAt = t
				}
			}
		}
		if createdAt.IsZero() {
			createdAt = time.Now()
		}

		if updateDateStr := record["FLD_UpdateDate"]; updateDateStr != "" {
			if parsedDate, err := ParseDate(updateDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					updatedAt = t
				}
			}
		}
		if updatedAt.IsZero() {
			updatedAt = createdAt
		}

		// Create user record for worker
		userRecord := user.User{
			Username:  username,
			Password:  string(hashedPassword),
			Name:      name,
			Dependent: nil, // No dependent information in legacy data
			CreatedAt: createdAt,
			UpdatedAt: updatedAt,
			DeletedAt: nil,
		}

		// Insert record (skip in dry run mode)
		if !dryRun {
			err := tx.WithContext(ctx).Create(&userRecord).Error
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Record %d: database error: %v", i+1, err))
				continue
			}
		}

		result.RecordsInserted++
	}

	return result, nil
}

// CheckExistingWorkers checks if worker users already exist in the database
func (m *WorkerMasterMigrator) CheckExistingWorkers(ctx context.Context, tx *gorm.DB) (map[string]bool, error) {
	var existingUsers []user.User
	err := tx.WithContext(ctx).Select("username").Find(&existingUsers).Error
	if err != nil {
		return nil, fmt.Errorf("failed to check existing worker users: %w", err)
	}

	existing := make(map[string]bool)
	for _, u := range existingUsers {
		existing[u.Username] = true
	}

	return existing, nil
}

// PreMigrationValidation performs validation before migration
func (m *WorkerMasterMigrator) PreMigrationValidation(ctx context.Context, tx *gorm.DB, records []map[string]string) error {
	// Check if table exists
	if !tx.Migrator().HasTable(&user.User{}) {
		return fmt.Errorf("user table does not exist - please run database migrations first")
	}

	// Check for duplicate worker codes in the input data
	workerCodes := make(map[string]int)
	for i, record := range records {
		code := record["FLD_S_CODE"]
		if code == "" {
			continue
		}

		if existingIndex, exists := workerCodes[code]; exists {
			return fmt.Errorf("duplicate worker code '%s' found in records %d and %d", code, existingIndex+1, i+1)
		}
		workerCodes[code] = i
	}

	return nil
}

// PostMigrationValidation performs validation after migration
func (m *WorkerMasterMigrator) PostMigrationValidation(ctx context.Context, tx *gorm.DB, expectedCount int) error {
	var count int64
	err := tx.WithContext(ctx).Model(&user.User{}).Where("deleted_at IS NULL").Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to count worker users: %w", err)
	}

	// Note: This count includes both regular users and workers
	// We expect at least the expectedCount additional users
	if int(count) < expectedCount {
		return fmt.Errorf("expected at least %d total users after worker migration, but found %d", expectedCount, count)
	}

	return nil
}

// GetDefaultPassword returns the default password used for migrated workers
func (m *WorkerMasterMigrator) GetDefaultPassword() string {
	return "worker123"
}
