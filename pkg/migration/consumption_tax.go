package migration

import (
	"context"
	"fmt"
	"time"

	consumption_tax "github.com/Sera-Global/be-nbs-accounting-system/domain/consumption_tax"
	"gorm.io/gorm"
)

// ConsumptionTaxParser handles parsing of 消費税率マスタ file
type ConsumptionTaxParser struct {
	BaseParser
}

// NewConsumptionTaxParser creates a new consumption tax parser
func NewConsumptionTaxParser() *ConsumptionTaxParser {
	return &ConsumptionTaxParser{
		BaseParser: BaseParser{tableName: "consumption_tax"},
	}
}

// Parse parses the consumption tax master file
func (p *ConsumptionTaxParser) Parse(filePath string) ([]map[string]string, error) {
	return p.ParseLegacyFile(filePath)
}

// Validate validates a consumption tax record
func (p *ConsumptionTaxParser) Validate(record map[string]string) error {
	requiredFields := []string{"FLD_ZeiFromData", "FLD_Zeiritsu"}
	return ValidateRequired(record, requiredFields)
}

// ConsumptionTaxMigrator handles migration of consumption tax data
type ConsumptionTaxMigrator struct {
	consumptionTaxDomain consumption_tax.ConsumptionTaxDomainItf
}

// NewConsumptionTaxMigrator creates a new consumption tax migrator
func NewConsumptionTaxMigrator() *ConsumptionTaxMigrator {
	// Initialize consumption tax domain
	consumptionTaxResource := consumption_tax.ConsumptionTaxResource{}
	consumptionTaxDomain := consumption_tax.InitConsumptionTaxDomain(consumptionTaxResource)

	return &ConsumptionTaxMigrator{
		consumptionTaxDomain: &consumptionTaxDomain,
	}
}

// GetDependencies returns the list of tables this migrator depends on
func (m *ConsumptionTaxMigrator) GetDependencies() []string {
	return []string{} // No dependencies
}

// Migrate migrates consumption tax records to the database
func (m *ConsumptionTaxMigrator) Migrate(ctx context.Context, tx *gorm.DB, records []map[string]string, dryRun bool) (*MigrationResult, error) {
	result := &MigrationResult{
		RecordsInserted: 0,
		Errors:          []string{},
	}

	// Track used codes to ensure uniqueness
	usedCodes := make(map[string]int)

	for i, record := range records {
		// Extract and transform data
		startDateStr := record["FLD_ZeiFromData"]
		if startDateStr == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing start date", i+1))
			continue
		}

		// Parse start date
		startDate, err := ParseDate(startDateStr)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: invalid start date: %v", i+1, err))
			continue
		}

		// Parse rate
		rate, err := ParseFloat(record["FLD_Zeiritsu"])
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: invalid rate: %v", i+1, err))
			continue
		}

		// Generate unique code from date
		baseCode := fmt.Sprintf("tax_%s", startDate)
		code := baseCode
		if count, exists := usedCodes[baseCode]; exists {
			code = fmt.Sprintf("%s_%d", baseCode, count+1)
		}
		usedCodes[baseCode]++

		// Parse dates for created_at and updated_at
		var createdAt, updatedAt time.Time
		if createDateStr := record["FLD_CreateDate"]; createDateStr != "" {
			if parsedDate, err := ParseDate(createDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					createdAt = t
				}
			}
		}
		if createdAt.IsZero() {
			createdAt = time.Now()
		}

		if updateDateStr := record["FLD_UpdateDate"]; updateDateStr != "" {
			if parsedDate, err := ParseDate(updateDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					updatedAt = t
				}
			}
		}
		if updatedAt.IsZero() {
			updatedAt = createdAt
		}

		// Parse start date as date type
		startDateParsed, err := time.Parse("2006-01-02", startDate)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: failed to parse start date: %v", i+1, err))
			continue
		}

		// Create consumption tax record
		consumptionTaxRecord := consumption_tax.ConsumptionTax{
			Code:      code,
			StartDate: startDateParsed,
			Rate:      rate,
			CreatedAt: createdAt,
			UpdatedAt: updatedAt,
			DeletedAt: nil,
		}

		// Insert record (skip in dry run mode)
		if !dryRun {
			err := tx.WithContext(ctx).Create(&consumptionTaxRecord).Error
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Record %d: database error: %v", i+1, err))
				continue
			}
		}

		result.RecordsInserted++
	}

	return result, nil
}

// CheckExistingConsumptionTax checks if consumption tax records already exist in the database
func (m *ConsumptionTaxMigrator) CheckExistingConsumptionTax(ctx context.Context, tx *gorm.DB) (map[string]bool, error) {
	var existingRecords []consumption_tax.ConsumptionTax
	err := tx.WithContext(ctx).Select("code").Find(&existingRecords).Error
	if err != nil {
		return nil, fmt.Errorf("failed to check existing consumption tax records: %w", err)
	}

	existing := make(map[string]bool)
	for _, record := range existingRecords {
		existing[record.Code] = true
	}

	return existing, nil
}

// PreMigrationValidation performs validation before migration
func (m *ConsumptionTaxMigrator) PreMigrationValidation(ctx context.Context, tx *gorm.DB, records []map[string]string) error {
	// Check if table exists
	if !tx.Migrator().HasTable(&consumption_tax.ConsumptionTax{}) {
		return fmt.Errorf("consumption_tax table does not exist - please run database migrations first")
	}

	// Check for duplicate dates in the input data
	dates := make(map[string]int)
	for i, record := range records {
		dateStr := record["FLD_ZeiFromData"]
		if dateStr == "" {
			continue
		}
		if existingIndex, exists := dates[dateStr]; exists {
			return fmt.Errorf("duplicate start date '%s' found in records %d and %d", dateStr, existingIndex+1, i+1)
		}
		dates[dateStr] = i
	}

	return nil
}

// PostMigrationValidation performs validation after migration
func (m *ConsumptionTaxMigrator) PostMigrationValidation(ctx context.Context, tx *gorm.DB, expectedCount int) error {
	var count int64
	err := tx.WithContext(ctx).Model(&consumption_tax.ConsumptionTax{}).Where("deleted_at IS NULL").Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to count consumption tax records: %w", err)
	}

	if int(count) < expectedCount {
		return fmt.Errorf("expected at least %d consumption tax records, but found %d", expectedCount, count)
	}

	return nil
}
