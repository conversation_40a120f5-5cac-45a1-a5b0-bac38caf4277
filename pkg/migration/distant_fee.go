package migration

import (
	"context"
	"fmt"
	"time"

	distantfee "github.com/Sera-Global/be-nbs-accounting-system/domain/distant_fee"
	"gorm.io/gorm"
)

// DistantFeeParser handles parsing of 遠方料金マスタ file
type DistantFeeParser struct {
	BaseParser
}

// NewDistantFeeParser creates a new distant fee parser
func NewDistantFeeParser() *DistantFeeParser {
	return &DistantFeeParser{
		BaseParser: BaseParser{tableName: "distant_fee"},
	}
}

// Parse parses the distant fee master file
func (p *DistantFeeParser) Parse(filePath string) ([]map[string]string, error) {
	return p.ParseLegacyFile(filePath)
}

// Validate validates a distant fee record
func (p *DistantFeeParser) Validate(record map[string]string) error {
	requiredFields := []string{"FLD_Block"}
	return ValidateRequired(record, requiredFields)
}

// DistantFeeMigrator handles migration of distant fee data
type DistantFeeMigrator struct {
	distantFeeDomain distantfee.DistantFeeDomainItf
}

// NewDistantFeeMigrator creates a new distant fee migrator
func NewDistantFeeMigrator() *DistantFeeMigrator {
	// Initialize distant fee domain
	distantFeeResource := distantfee.DistantFeeResource{}
	distantFeeDomain := distantfee.InitDistantFeeDomain(distantFeeResource)

	return &DistantFeeMigrator{
		distantFeeDomain: &distantFeeDomain,
	}
}

// GetDependencies returns the list of tables this migrator depends on
func (m *DistantFeeMigrator) GetDependencies() []string {
	return []string{} // No dependencies
}

// Migrate migrates distant fee records to the database
func (m *DistantFeeMigrator) Migrate(ctx context.Context, tx *gorm.DB, records []map[string]string, dryRun bool) (*MigrationResult, error) {
	result := &MigrationResult{
		RecordsInserted: 0,
		Errors:          []string{},
	}

	// Track used codes to ensure uniqueness
	usedCodes := make(map[string]int)

	for i, record := range records {
		// Extract and transform data
		blockCode := record["FLD_Block"]
		if blockCode == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing block code", i+1))
			continue
		}

		// Use block code as the base for our code
		baseCode := fmt.Sprintf("distant_fee_%s", blockCode)
		code := baseCode
		if count, exists := usedCodes[baseCode]; exists {
			code = fmt.Sprintf("%s_%d", baseCode, count+1)
		}
		usedCodes[baseCode]++

		// Parse unit value
		unit, err := ParseFloat(record["FLD_Unit"])
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: invalid unit value: %v", i+1, err))
			continue
		}

		// Parse amount per hour
		addAmountPerHour, err := ParseFloat(record["FLD_Kingaku_1"])
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: invalid amount per hour: %v", i+1, err))
			continue
		}

		// Parse dates for created_at and updated_at
		var createdAt, updatedAt time.Time
		if createDateStr := record["FLD_CreateDate"]; createDateStr != "" {
			if parsedDate, err := ParseDate(createDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					createdAt = t
				}
			}
		}
		if createdAt.IsZero() {
			createdAt = time.Now()
		}

		if updateDateStr := record["FLD_UpdateDate"]; updateDateStr != "" {
			if parsedDate, err := ParseDate(updateDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					updatedAt = t
				}
			}
		}
		if updatedAt.IsZero() {
			updatedAt = createdAt
		}

		// Create distant fee record
		distantFeeRecord := distantfee.DistantFee{
			Code:             code,
			Title:            fmt.Sprintf("Distance Fee for Block %s", blockCode),
			Explanation:      fmt.Sprintf("Distance fee configuration for block %s", blockCode),
			Unit:             unit,
			AddAmountPerHour: addAmountPerHour,
			CreatedAt:        createdAt,
			UpdatedAt:        updatedAt,
			DeletedAt:        nil,
		}

		// Insert record (skip in dry run mode)
		if !dryRun {
			err := tx.WithContext(ctx).Create(&distantFeeRecord).Error
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Record %d: database error: %v", i+1, err))
				continue
			}
		}

		result.RecordsInserted++
	}

	return result, nil
}

// CheckExistingDistantFees checks if distant fee records already exist in the database
func (m *DistantFeeMigrator) CheckExistingDistantFees(ctx context.Context, tx *gorm.DB) (map[string]bool, error) {
	var existingRecords []distantfee.DistantFee
	err := tx.WithContext(ctx).Select("code").Find(&existingRecords).Error
	if err != nil {
		return nil, fmt.Errorf("failed to check existing distant fee records: %w", err)
	}

	existing := make(map[string]bool)
	for _, record := range existingRecords {
		existing[record.Code] = true
	}

	return existing, nil
}

// PreMigrationValidation performs validation before migration
func (m *DistantFeeMigrator) PreMigrationValidation(ctx context.Context, tx *gorm.DB, records []map[string]string) error {
	// Check if table exists
	if !tx.Migrator().HasTable(&distantfee.DistantFee{}) {
		return fmt.Errorf("distant_fee table does not exist - please run database migrations first")
	}

	// Check for duplicate block codes in the input data
	blocks := make(map[string]int)
	for i, record := range records {
		block := record["FLD_Block"]
		if block == "" {
			continue
		}

		if existingIndex, exists := blocks[block]; exists {
			return fmt.Errorf("duplicate block code '%s' found in records %d and %d", block, existingIndex+1, i+1)
		}
		blocks[block] = i
	}

	return nil
}

// PostMigrationValidation performs validation after migration
func (m *DistantFeeMigrator) PostMigrationValidation(ctx context.Context, tx *gorm.DB, expectedCount int) error {
	var count int64
	err := tx.WithContext(ctx).Model(&distantfee.DistantFee{}).Where("deleted_at IS NULL").Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to count distant fee records: %w", err)
	}

	if int(count) < expectedCount {
		return fmt.Errorf("expected at least %d distant fee records, but found %d", expectedCount, count)
	}

	return nil
}
