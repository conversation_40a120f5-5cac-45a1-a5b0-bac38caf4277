package migration

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/domain/customer"
	"github.com/Sera-Global/be-nbs-accounting-system/domain/department"
	department_pic "github.com/Sera-Global/be-nbs-accounting-system/domain/department_pic"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
	"gorm.io/gorm"
)

// CustomerMasterParser handles parsing of 顧客マスタ_Ordering Company Master file
type CustomerMasterParser struct {
	BaseParser
}

// NewCustomerMasterParser creates a new customer master parser
func NewCustomerMasterParser() *CustomerMasterParser {
	return &CustomerMasterParser{
		BaseParser: BaseParser{tableName: "customer"},
	}
}

// Parse parses the customer master file
func (p *CustomerMasterParser) Parse(filePath string) ([]map[string]string, error) {
	return p.ParseLegacyFile(filePath)
}

// Validate validates a customer master record
func (p *CustomerMasterParser) Validate(record map[string]string) error {
	requiredFields := []string{"FLD_TokuisakiCode", "FLD_TokuisakiName"}
	return ValidateRequired(record, requiredFields)
}

// CustomerMasterMigrator handles migration of customer master data
type CustomerMasterMigrator struct {
	customerDomain      customer.CustomerDomainItf
	departmentDomain    department.DepartmentDomainItf
	departmentPicDomain department_pic.DepartmentPicDomainItf
}

// NewCustomerMasterMigrator creates a new customer master migrator
func NewCustomerMasterMigrator() *CustomerMasterMigrator {
	// Initialize domains
	customerResource := customer.CustomerResource{}
	customerDomain := customer.InitCustomerDomain(customerResource)

	departmentResource := department.DepartmentResource{}
	departmentDomain := department.InitDepartmentDomain(departmentResource)

	departmentPicResource := department_pic.DepartmentPicResource{}
	departmentPicDomain := department_pic.InitDepartmentPicDomain(departmentPicResource)

	return &CustomerMasterMigrator{
		customerDomain:      &customerDomain,
		departmentDomain:    &departmentDomain,
		departmentPicDomain: &departmentPicDomain,
	}
}

// GetDependencies returns the list of tables this migrator depends on
func (m *CustomerMasterMigrator) GetDependencies() []string {
	return []string{"statutory"} // May need statutory table for statutory_id
}

// Migrate migrates customer master records to the database
func (m *CustomerMasterMigrator) Migrate(ctx context.Context, tx *gorm.DB, records []map[string]string, dryRun bool) (*MigrationResult, error) {
	result := &MigrationResult{
		RecordsInserted: 0,
		Errors:          []string{},
	}

	// Track used codes to ensure uniqueness
	usedCodes := make(map[string]int)

	for i, record := range records {
		// Extract and transform data
		code := record["FLD_TokuisakiCode"]
		name := record["FLD_TokuisakiName"]

		if code == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing customer code", i+1))
			continue
		}

		if name == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing customer name", i+1))
			continue
		}

		// Ensure code uniqueness
		baseCode := code
		if count, exists := usedCodes[baseCode]; exists {
			code = fmt.Sprintf("%s_%d", baseCode, count+1)
		}
		usedCodes[baseCode]++

		// Parse billing date
		var billingDate *time.Time
		if shimeDateStr := record["FLD_ShimeCode"]; shimeDateStr != "" {
			if day, err := strconv.Atoi(shimeDateStr); err == nil && day >= 1 && day <= 31 {
				// Create a date with the day of month (using current year/month as placeholder)
				now := time.Now()
				date := time.Date(now.Year(), now.Month(), day, 0, 0, 0, 0, time.UTC)
				billingDate = &date
			}
		}

		// Parse statutory ID (if available)
		var statutoryID *int64
		if tankaCodeStr := record["FLD_TankaCode"]; tankaCodeStr != "" {
			if id, err := strconv.ParseInt(tankaCodeStr, 10, 64); err == nil {
				statutoryID = &id
			}
		}

		// Parse dates for created_at and updated_at
		var createdAt, updatedAt time.Time
		if createDateStr := record["FLD_CreateDate"]; createDateStr != "" {
			if parsedDate, err := ParseDate(createDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					createdAt = t
				}
			}
		}
		if createdAt.IsZero() {
			createdAt = time.Now()
		}

		if updateDateStr := record["FLD_UpdateDate"]; updateDateStr != "" {
			if parsedDate, err := ParseDate(updateDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					updatedAt = t
				}
			}
		}
		if updatedAt.IsZero() {
			updatedAt = createdAt
		}

		// Create customer record
		customerRecord := customer.Customer{
			Code:              code,
			Name:              name,
			Furigana:          record["FLD_Furigana"],
			PostCode:          record["FLD_YubinNo"],
			AddressPrefecture: record["FLD_Address1"],
			AddressCity:       record["FLD_Address2"],
			AddressBuilding:   record["FLD_Address3"],
			Telephone:         record["FLD_TelNo"],
			Fax:               record["FLD_FaxNo"],
			BillingDate:       parse.TimePointer(billingDate),
			StatutoryID:       parse.Int64Pointer(statutoryID),
			CreatedAt:         createdAt,
			UpdatedAt:         updatedAt,
			DeletedAt:         nil,
		}

		// Insert customer record (skip in dry run mode)
		if !dryRun {
			err := tx.WithContext(ctx).Create(&customerRecord).Error
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Record %d: database error: %v", i+1, err))
				continue
			}
		}

		result.RecordsInserted++
	}

	return result, nil
}

// CheckExistingCustomers checks if customers already exist in the database
func (m *CustomerMasterMigrator) CheckExistingCustomers(ctx context.Context, tx *gorm.DB) (map[string]bool, error) {
	var existingCustomers []customer.Customer
	err := tx.WithContext(ctx).Select("code").Find(&existingCustomers).Error
	if err != nil {
		return nil, fmt.Errorf("failed to check existing customers: %w", err)
	}

	existing := make(map[string]bool)
	for _, c := range existingCustomers {
		existing[c.Code] = true
	}

	return existing, nil
}

// PreMigrationValidation performs validation before migration
func (m *CustomerMasterMigrator) PreMigrationValidation(ctx context.Context, tx *gorm.DB, records []map[string]string) error {
	// Check if table exists
	if !tx.Migrator().HasTable(&customer.Customer{}) {
		return fmt.Errorf("customer table does not exist - please run database migrations first")
	}

	// Check for duplicate customer codes in the input data
	codes := make(map[string]int)
	for i, record := range records {
		code := record["FLD_TokuisakiCode"]
		if code == "" {
			continue
		}

		if existingIndex, exists := codes[code]; exists {
			return fmt.Errorf("duplicate customer code '%s' found in records %d and %d", code, existingIndex+1, i+1)
		}
		codes[code] = i
	}

	return nil
}

// PostMigrationValidation performs validation after migration
func (m *CustomerMasterMigrator) PostMigrationValidation(ctx context.Context, tx *gorm.DB, expectedCount int) error {
	var count int64
	err := tx.WithContext(ctx).Model(&customer.Customer{}).Where("deleted_at IS NULL").Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to count customers: %w", err)
	}

	if int(count) < expectedCount {
		return fmt.Errorf("expected at least %d customers, but found %d", expectedCount, count)
	}

	return nil
}
