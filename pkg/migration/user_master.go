package migration

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/domain/user"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UserMasterParser handles parsing of ユーザーマスタ file
type UserMasterParser struct {
	BaseParser
}

// NewUserMasterParser creates a new user master parser
func NewUserMasterParser() *UserMasterParser {
	return &UserMasterParser{
		BaseParser: BaseParser{tableName: "user"},
	}
}

// Parse parses the user master file
func (p *UserMasterParser) Parse(filePath string) ([]map[string]string, error) {
	return p.ParseLegacyFile(filePath)
}

// Validate validates a user master record
func (p *UserMasterParser) Validate(record map[string]string) error {
	requiredFields := []string{"FLD_UserID"}
	return ValidateRequired(record, requiredFields)
}

// UserMasterMigrator handles migration of user master data
type UserMasterMigrator struct {
	userDomain user.UserDomainItf
}

// NewUserMasterMigrator creates a new user master migrator
func NewUserMasterMigrator() *UserMasterMigrator {
	// Initialize user domain
	userResource := user.UserResource{}
	userDomain := user.InitUserDomain(userResource)

	return &UserMasterMigrator{
		userDomain: &userDomain,
	}
}

// GetDependencies returns the list of tables this migrator depends on
func (m *UserMasterMigrator) GetDependencies() []string {
	return []string{} // No dependencies
}

// Migrate migrates user master records to the database
func (m *UserMasterMigrator) Migrate(ctx context.Context, tx *gorm.DB, records []map[string]string, dryRun bool) (*MigrationResult, error) {
	result := &MigrationResult{
		RecordsInserted: 0,
		Errors:          []string{},
	}

	// Track used usernames to ensure uniqueness
	usedUsernames := make(map[string]int)

	for i, record := range records {
		// Extract and transform data
		name := record["FLD_UserName"]
		userIDFull := record["FLD_UserID"]

		if name == "" {
			name = userIDFull
		}

		if userIDFull == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing user ID", i+1))
			continue
		}

		// Extract username from userID (before "/" if present)
		username := userIDFull
		if slashIndex := strings.Index(userIDFull, "/"); slashIndex != -1 {
			username = userIDFull[:slashIndex]
		}

		// Ensure username uniqueness
		baseUsername := username
		if count, exists := usedUsernames[baseUsername]; exists {
			username = fmt.Sprintf("%s_%d", baseUsername, count+1)
		}
		usedUsernames[baseUsername]++

		// Generate default password hash
		defaultPassword := "password123"
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(defaultPassword), bcrypt.DefaultCost)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: failed to hash password: %v", i+1, err))
			continue
		}

		// Parse dates for created_at and updated_at
		var createdAt, updatedAt time.Time
		if createDateStr := record["FLD_CreateDate"]; createDateStr != "" {
			if parsedDate, err := ParseDate(createDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					createdAt = t
				}
			}
		}
		if createdAt.IsZero() {
			createdAt = time.Now()
		}

		if updateDateStr := record["FLD_UpdateDate"]; updateDateStr != "" {
			if parsedDate, err := ParseDate(updateDateStr); err == nil && parsedDate != "" {
				if t, err := time.Parse("2006-01-02", parsedDate); err == nil {
					updatedAt = t
				}
			}
		}
		if updatedAt.IsZero() {
			updatedAt = createdAt
		}

		// Create user record
		userRecord := user.User{
			Username:  username,
			Password:  string(hashedPassword),
			Name:      name,
			Dependent: nil, // No dependent information in legacy data
			CreatedAt: createdAt,
			UpdatedAt: updatedAt,
			DeletedAt: nil,
		}

		// Insert record (skip in dry run mode)
		if !dryRun {
			err := tx.WithContext(ctx).Create(&userRecord).Error
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Record %d: database error: %v", i+1, err))
				continue
			}
		}

		result.RecordsInserted++
	}

	return result, nil
}

// CheckExistingUsers checks if users already exist in the database
func (m *UserMasterMigrator) CheckExistingUsers(ctx context.Context, tx *gorm.DB) (map[string]bool, error) {
	var existingUsers []user.User
	err := tx.WithContext(ctx).Select("username").Find(&existingUsers).Error
	if err != nil {
		return nil, fmt.Errorf("failed to check existing users: %w", err)
	}

	existing := make(map[string]bool)
	for _, u := range existingUsers {
		existing[u.Username] = true
	}

	return existing, nil
}

// PreMigrationValidation performs validation before migration
func (m *UserMasterMigrator) PreMigrationValidation(ctx context.Context, tx *gorm.DB, records []map[string]string) error {
	// Check if table exists
	if !tx.Migrator().HasTable(&user.User{}) {
		return fmt.Errorf("user table does not exist - please run database migrations first")
	}

	// Check for duplicate usernames in the input data (after processing)
	processedUsernames := make(map[string]int)
	for i, record := range records {
		userIDFull := record["FLD_UserID"]
		if userIDFull == "" {
			continue
		}

		// Extract username from userID (before "/" if present)
		username := userIDFull
		if slashIndex := strings.Index(userIDFull, "/"); slashIndex != -1 {
			username = userIDFull[:slashIndex]
		}

		if existingIndex, exists := processedUsernames[username]; exists {
			return fmt.Errorf("duplicate username '%s' found in records %d and %d", username, existingIndex+1, i+1)
		}
		processedUsernames[username] = i
	}

	return nil
}

// PostMigrationValidation performs validation after migration
func (m *UserMasterMigrator) PostMigrationValidation(ctx context.Context, tx *gorm.DB, expectedCount int) error {
	var count int64
	err := tx.WithContext(ctx).Model(&user.User{}).Where("deleted_at IS NULL").Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to count users: %w", err)
	}

	if int(count) < expectedCount {
		return fmt.Errorf("expected at least %d users, but found %d", expectedCount, count)
	}

	return nil
}

// GetDefaultPassword returns the default password used for migrated users
func (m *UserMasterMigrator) GetDefaultPassword() string {
	return "password123"
}
