package user

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
)

type User struct {
	ID                  int64      `gorm:"column:id;primary_key"`
	Username            string     `gorm:"column:username;unique"`
	Password            string     `gorm:"column:password"`
	Name                string     `gorm:"column:name"`
	Dependent           *string    `gorm:"column:dependent"`
	HourlyWageID        int64      `gorm:"column:hourly_wage_id"`
	OvertimeAllowanceID int64      `gorm:"column:overtime_allowance_id"`
	CreatedAt           time.Time  `gorm:"column:created_at"`
	UpdatedAt           time.Time  `gorm:"column:updated_at"`
	DeletedAt           *time.Time `gorm:"column:deleted_at"`
}

// GetWorkerListReq represents the request parameters for getting worker list
type GetWorkerListReq struct {
	types.BasicGetParam
}

// WorkerListItem represents a worker item in the list response
type WorkerListItem struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}
