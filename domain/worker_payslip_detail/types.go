package worker_payslip_detail

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/domain/worker_payslip"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/utils"
)

type WorkerPayslipDetail struct {
	ID                     int64            `gorm:"column:id;primary_key"`
	WorkerPayslipID        int64            `gorm:"column:worker_payslip_id"`
	WorkDate               time.Time        `gorm:"column:work_date"`
	SiteName               string           `gorm:"column:site_name"`
	StartTime              utils.LocalTime  `gorm:"column:start_time"`
	EndTime                utils.LocalTime  `gorm:"column:end_time"`
	BreakTime              *utils.LocalTime `gorm:"column:break_time"`
	ExpensePerWorker       float64          `gorm:"column:expense_per_worker"`
	TransportationExpense  float64          `gorm:"column:transportation_expense"`
	LeaderAllowance        float64          `gorm:"column:leader_allowance"`
	DistantFeeAmount       float64          `gorm:"column:distant_fee_amount"`
	QualificationAllowance float64          `gorm:"column:qualification_allowance"`
	IncomeTaxAmount        float64          `gorm:"column:income_tax_amount"`

	// Foreign key relationship
	WorkerPayslip worker_payslip.WorkerPayslip `gorm:"foreignkey:WorkerPayslipID"`
}

type CreateParam struct {
	WorkerPayslipID        int64            `json:"worker_payslip_id" validate:"required"`
	WorkDate               time.Time        `json:"work_date" validate:"required"`
	SiteName               string           `json:"site_name" validate:"required"`
	StartTime              utils.LocalTime  `json:"start_time" validate:"required"`
	EndTime                utils.LocalTime  `json:"end_time" validate:"required"`
	BreakTime              *utils.LocalTime `json:"break_time"`
	ExpensePerWorker       float64          `json:"expense_per_worker"`
	TransportationExpense  float64          `json:"transportation_expense"`
	LeaderAllowance        float64          `json:"leader_allowance"`
	DistantFeeAmount       float64          `json:"distant_fee_amount"`
	QualificationAllowance float64          `json:"qualification_allowance"`
	IncomeTaxAmount        float64          `json:"income_tax_amount"`
}

// GetDetailsByPayslipIDsParam represents the parameters for getting worker payslip details by payslip IDs
type GetDetailsByPayslipIDsParam struct {
	PayslipIDs []int64
}
