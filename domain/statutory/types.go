package statutory

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
)

type Statutory struct {
	ID        int64      `gorm:"column:id;primary_key"`
	Code      string     `gorm:"column:code;unique"`
	Title     string     `gorm:"column:title"`
	Rate      float64    `gorm:"column:rate"`
	CreatedAt time.Time  `gorm:"column:created_at"`
	UpdatedAt time.Time  `gorm:"column:updated_at"`
	DeletedAt *time.Time `gorm:"column:deleted_at"`
}

type GetListReq struct {
	types.BasicGetParam
}

// GetByCustomerIDParam represents the parameters for getting statutory by customer ID
type GetByCustomerIDParam struct {
	CustomerID int64
}
