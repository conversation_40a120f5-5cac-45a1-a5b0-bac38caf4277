package worker_payslip

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// bulkCreateWithTx inserts multiple worker payslip records into the database.
func (rsc WorkerPayslipResource) bulkCreateWithTx(ctx context.Context, tx *gorm.DB, param []CreateParam) ([]WorkerPayslip, error) {
	db := tx.WithContext(ctx)
	var payslips []WorkerPayslip

	for _, v := range param {
		payslip := WorkerPayslip{
			SiteReportWorkerIDs: pq.Int64Array(v.SiteReportWorkerIDs),
			WorkerName:          v.WorkerName,
			IssuedDate:          v.IssuedDate,
			DebtPayment:         v.DebtPayment,
			TaxableSalary:       v.TaxableSalary,
			TaxFreeSalary:       v.TaxFreeSalary,
			IncomeTaxAmount:     v.IncomeTaxAmount,
			TotalPayment:        v.TotalPayment,
		}

		payslips = append(payslips, payslip)
	}

	err := db.Create(&payslips).Error
	if err != nil {
		return []WorkerPayslip{}, log.LogError(err, nil)
	}

	return payslips, nil
}

// getPayslipList retrieves worker payslip records filtered by date range and optional search term.
func (rsc WorkerPayslipResource) getPayslipList(ctx context.Context, param GetPayslipListParam) ([]WorkerPayslip, error) {
	var payslips []WorkerPayslip

	db := dbmanager.Manager().WithContext(ctx)

	query := db.Where("issued_date >= ? AND issued_date <= ?", param.StartDate, param.EndDate)

	// Apply search filter if provided
	if param.Search != "" {
		query = query.Where("worker_name ILIKE ?", "%"+param.Search+"%")
	}

	err := query.Order("issued_date ASC, id ASC").Find(&payslips).Error
	if err != nil {
		return []WorkerPayslip{}, log.LogError(err, nil)
	}

	return payslips, nil
}

// getPayslipsByIDs retrieves worker payslip records by their IDs.
func (rsc WorkerPayslipResource) getPayslipsByIDs(ctx context.Context, param GetPayslipsByIDsParam) ([]WorkerPayslip, error) {
	var payslips []WorkerPayslip

	if len(param.IDs) == 0 {
		return payslips, nil
	}

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("id IN ?", param.IDs).Order("id ASC").Find(&payslips).Error
	if err != nil {
		return []WorkerPayslip{}, log.LogError(err, nil)
	}

	return payslips, nil
}
