package worker_payslip

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"gorm.io/gorm"
)

type (
	WorkerPayslipDomainItf interface {
		BulkCreateWithTx(ctx context.Context, tx *gorm.DB, param []CreateParam) ([]WorkerPayslip, error)
		GetPayslipList(ctx context.Context, param GetPayslipListParam) ([]WorkerPayslip, error)
		GetPayslipsByIDs(ctx context.Context, param GetPayslipsByIDsParam) ([]WorkerPayslip, error)
	}

	WorkerPayslipResourceItf interface {
		bulkCreateWithTx(ctx context.Context, tx *gorm.DB, param []CreateParam) ([]WorkerPayslip, error)
		getPayslipList(ctx context.Context, param GetPayslipListParam) ([]WorkerPayslip, error)
		getPayslipsByIDs(ctx context.Context, param GetPayslipsByIDsParam) ([]WorkerPayslip, error)
	}
)

// BulkCreateWithTx inserts multiple worker payslip records in a single transaction.
func (d *WorkerPayslipDomain) BulkCreateWithTx(ctx context.Context, tx *gorm.DB, param []CreateParam) ([]WorkerPayslip, error) {
	payslip, err := d.resource.bulkCreateWithTx(ctx, tx, param)
	if err != nil {
		return []WorkerPayslip{}, log.LogError(err, nil)
	}

	return payslip, nil
}

// GetPayslipList retrieves worker payslip records filtered by date range and optional search term.
func (d *WorkerPayslipDomain) GetPayslipList(ctx context.Context, param GetPayslipListParam) ([]WorkerPayslip, error) {
	payslips, err := d.resource.getPayslipList(ctx, param)
	if err != nil {
		return []WorkerPayslip{}, log.LogError(err, nil)
	}

	return payslips, nil
}

// GetPayslipsByIDs retrieves worker payslip records by their IDs.
func (d *WorkerPayslipDomain) GetPayslipsByIDs(ctx context.Context, param GetPayslipsByIDsParam) ([]WorkerPayslip, error) {
	payslips, err := d.resource.getPayslipsByIDs(ctx, param)
	if err != nil {
		return []WorkerPayslip{}, log.LogError(err, nil)
	}

	return payslips, nil
}
