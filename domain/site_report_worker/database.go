package sitereportworker

import (
	"context"
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/utils"
	"gorm.io/gorm"
)

// createWorkerWithTx creates a new site report worker record within a transaction.
func (rsc SiteReportWorkerResource) createWorkerWithTx(ctx context.Context, tx *gorm.DB, param CreateWorkerParam) (int64, error) {
	db := tx.WithContext(ctx)

	// Convert time.Time to utils.LocalTime
	var breakTime *utils.LocalTime
	if param.BreakTime != nil {
		breakTime = &utils.LocalTime{Time: *param.BreakTime}
	}

	worker := SiteReportWorker{
		SiteReportID:          param.SiteReportID,
		UserID:                param.UserID,
		StartTime:             utils.LocalTime{Time: param.StartTime},
		EndTime:               utils.LocalTime{Time: param.EndTime},
		BreakTime:             breakTime,
		TransportationExpense: param.TransportationExpense,
		LeaderAllowance:       param.LeaderAllowance,
		DistantFeeID:          param.DistantFeeID,
		IncomeTaxID:           param.IncomeTaxID,
		Tax:                   param.Tax,
		Amount:                param.Amount,
		Snapshot:              param.Snapshot,
		Status:                param.Status,
		IssuedDate:            param.IssuedDate,
	}

	err := db.Create(&worker).Error
	if err != nil {
		return 0, log.LogError(err, nil)
	}

	return worker.ID, nil
}

// updateWorker updates an existing site report worker record.
func (rsc SiteReportWorkerResource) updateWorkerWithTx(ctx context.Context, tx *gorm.DB, param UpdateWorkerParam) error {
	db := tx.WithContext(ctx)

	updateFields := map[string]interface{}{
		"user_id":                param.UserID,
		"start_time":             param.StartTime,
		"end_time":               param.EndTime,
		"break_time":             param.BreakTime,
		"transportation_expense": param.TransportationExpense,
		"leader_allowance":       param.LeaderAllowance,
		"distant_fee_id":         param.DistantFeeID,
		"income_tax_id":          param.IncomeTaxID,
		"tax":                    param.Tax,
		"amount":                 param.Amount,
		"snapshot":               param.Snapshot,
	}

	err := db.Model(&SiteReportWorker{}).
		Where("id = ?", param.ID).
		Updates(updateFields).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// deleteWorker deletes a site report worker record.
func (rsc SiteReportWorkerResource) deleteWorkerWithTx(ctx context.Context, tx *gorm.DB, param DeleteWorkerParam) error {
	db := tx.WithContext(ctx)

	err := db.Delete(&SiteReportWorker{}, param.ID).Error
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// getBySiteReportID retrieves site report worker records by site report ID.
func (rsc SiteReportWorkerResource) getBySiteReportID(ctx context.Context, siteReportID int64) ([]SiteReportWorker, error) {
	var workers []SiteReportWorker

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("site_report_id = ?", siteReportID).
		Find(&workers).Error

	if err != nil {
		return []SiteReportWorker{}, log.LogError(err, nil)
	}

	return workers, nil
}

// getWorkerReportList retrieves site report worker records with site report and user data filtered by date range.
func (rsc SiteReportWorkerResource) getWorkerReportList(ctx context.Context, param GetWorkerReportListParam) ([]WorkerReportListItem, error) {
	var workers []WorkerReportListItem

	db := dbmanager.Manager().WithContext(ctx)

	// Join with site_report table to filter by work_date and exclude soft deleted records
	// Select specific fields from both tables to avoid circular import
	err := db.Table("site_report_worker").
		Select("site_report_worker.*, site_report.site_name, site_report.work_date").
		Joins("JOIN site_report ON site_report_worker.site_report_id = site_report.id").
		Where("site_report.work_date >= ? AND site_report.work_date <= ?", param.StartDate, param.EndDate).
		Where("site_report.deleted_at IS NULL").
		Preload("User").
		Order("site_report.work_date ASC, site_report_worker.id ASC").
		Find(&workers).Error

	if err != nil {
		return []WorkerReportListItem{}, log.LogError(err, nil)
	}

	return workers, nil
}

// bulkUpdateWorkerPaymentStatusWithTx updates the payment status and issued_date for multiple site report worker records.
func (rsc SiteReportWorkerResource) bulkUpdateWorkerPaymentStatusWithTx(ctx context.Context, tx *gorm.DB, param BulkUpdateWorkerPaymentStatusParam) error {
	db := tx.WithContext(ctx)

	// Build update map based on payment status
	updateFields := map[string]interface{}{}

	if param.IsPaid {
		// When is_paid is true: set status to PAID and issued_date to current timestamp
		updateFields["status"] = StatusPaid
		updateFields["issued_date"] = time.Now()
	} else {
		// When is_paid is false: set status to UNPAID and issued_date to null
		updateFields["status"] = StatusUnpaid
		updateFields["issued_date"] = nil
	}

	// Perform bulk update for the specified site report worker IDs
	err := db.Model(&SiteReportWorker{}).
		Where("id IN ?", param.SiteReportWorkerIDs).
		Updates(updateFields).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// getByIDs retrieves site report worker records by IDs.
func (rsc SiteReportWorkerResource) getByIDs(ctx context.Context, ids []int64) ([]SiteReportWorker, error) {
	var workers []SiteReportWorker

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("id IN ?", ids).
		Preload("User").
		Preload("Qualifications").
		Find(&workers).Error

	if err != nil {
		return []SiteReportWorker{}, log.LogError(err, nil)
	}

	return workers, nil
}
