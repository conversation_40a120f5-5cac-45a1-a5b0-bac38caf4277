# Data Migration Analysis - Legacy to New Schema

## Overview
This document provides a comprehensive analysis of the legacy data files in `nbs_master_data/` and their mapping to the new database schema defined in `files/database/main_migration.sql`.

## Legacy Data File Analysis

### Data Format Pattern
All legacy data files follow a consistent format:
- Field-value pairs: `FLD_FieldName: value`
- Records separated by blank lines
- Metadata fields: `$UpdatedBy`, `$Revisions`, `FLD_CreateDate`, `FLD_LastUpdate`, `FLD_UpdateDate`
- Text encoding appears to be Japanese (Shift-JIS or similar)

### Legacy Data Files Inventory

| File Name | Records | Primary Purpose | Target Table(s) |
|-----------|---------|-----------------|-----------------|
| ユーザーマスタ | ~26 | User accounts | `user`, `user_role` |
| 顧客マスタ_Ordering Company Master | ~324 | Customer data | `customer`, `department`, `department_pic` |
| 会社マスタ_Company Master | ~2 | Company info | No direct mapping |
| 作業員マスタ | ~338 | Worker data | `user` |
| 資格マスタ | ~26 | Qualifications | `qualification` |
| 地区マスタ | ~508 | Districts | `district`, `block` |
| 消費税率マスタ | ~2 | Tax rates | `consumption_tax` |
| 所得税税額マスタ | ~632 | Income tax | `income_tax` |
| 遠方出張諸手当マスタ | ~110 | Travel allowances | `distant_travel_allowance` |
| 遠方料金マスタ | ~68 | Distance fees | `distant_fee` |
| 料金設定 | ~1 | Pricing config | `basic_price` |
| 番号管理マスタ | ~1 | Number management | No direct mapping |

## Detailed Field Mappings

### 1. ユーザーマスタ → `user` table
| Legacy Field | New Column | Type | Notes |
|--------------|------------|------|-------|
| FLD_UserName | name | varchar | Direct mapping |
| FLD_UserID | username | varchar | Extract before "/" |
| FLD_KyoName | - | - | Company name, not needed |
| FLD_CreateDate | created_at | timestamptz | Parse date format |
| FLD_UpdateDate | updated_at | timestamptz | Parse date format |
| - | password | varchar | Generate default hash |
| - | dependent | varchar | NULL |

### 2. 顧客マスタ_Ordering Company Master → `customer` table
| Legacy Field | New Column | Type | Notes |
|--------------|------------|------|-------|
| FLD_TokuisakiCode | code | varchar | Direct mapping |
| FLD_TokuisakiName | name | varchar | Direct mapping |
| FLD_Furigana | furigana | varchar | Direct mapping |
| FLD_YubinNo | post_code | varchar | Direct mapping |
| FLD_Address1 | address_prefecture | varchar | Direct mapping |
| FLD_Address2 | address_city | varchar | Direct mapping |
| FLD_Address3 | address_building | varchar | Direct mapping |
| FLD_TelNo | telephone | varchar | Direct mapping |
| FLD_FaxNo | fax | varchar | Direct mapping |
| FLD_ShimeCode | billing_date | date | Convert to date (day of month) |
| FLD_TankaCode | statutory_id | int8 | Lookup statutory by code |

### 3. 作業員マスタ → `user` table
| Legacy Field | New Column | Type | Notes |
|--------------|------------|------|-------|
| FLD_S_CODE | username | varchar | Direct mapping |
| FLD_S_NAME | name | varchar | Direct mapping |
| FLD_S_KANA | - | - | Not used in new schema |
| - | password | varchar | Generate default hash |
| - | dependent | varchar | NULL |

### 4. 資格マスタ → `qualification` table
| Legacy Field | New Column | Type | Notes |
|--------------|------------|------|-------|
| FLD_SikakuName | title | varchar | Direct mapping |
| - | code | varchar | Generate from title |
| - | explanation | text | Empty string |
| FLD_Kingaku | add_claim | float64 | Direct mapping |
| FLD_SH_Kingaku | paid_amount | float64 | Direct mapping |

### 5. 地区マスタ → `district` + `block` tables
| Legacy Field | New Column | Type | Notes |
|--------------|------------|------|-------|
| FLD_TikuName | district.name | varchar | Direct mapping |
| FLD_Block | block.code | varchar | Create/lookup block |
| - | district.code | varchar | Generate from name |
| - | block.name | varchar | Use block code as name |
| - | block.unit_price | float | Default 0 |
| - | block.price_per_worker | float | Default 0 |

### 6. 消費税率マスタ → `consumption_tax` table
| Legacy Field | New Column | Type | Notes |
|--------------|------------|------|-------|
| FLD_ZeiFromData | start_date | date | Parse date |
| FLD_Zeiritsu | rate | float | Direct mapping |
| - | code | varchar | Generate from date |

### 7. 所得税税額マスタ → `income_tax` table
| Legacy Field | New Column | Type | Notes |
|--------------|------------|------|-------|
| m01 | more_than | float | Direct mapping |
| m02 | less_than | float | Direct mapping |
| mZeiGaku | amount | json | Store as JSON object |
| - | code | varchar | Generate from range |

### 8. 遠方出張諸手当マスタ → `distant_travel_allowance` table
| Legacy Field | New Column | Type | Notes |
|--------------|------------|------|-------|
| FLD_Block | code | varchar | Direct mapping |
| - | title | varchar | Use block code |
| - | explanation | text | Empty string |
| EmKinM01-08 | add_claim | float | Sum all EmKinM values |
| EmKinM01-08 | add_paid | float | Sum all EmKinM values |

### 9. 遠方料金マスタ → `distant_fee` table
| Legacy Field | New Column | Type | Notes |
|--------------|------------|------|-------|
| FLD_Block | code | varchar | Direct mapping |
| - | title | varchar | Use block code |
| - | explanation | text | Empty string |
| FLD_Unit | unit | float | Direct mapping |
| FLD_Kingaku_1 | add_amount_per_hour | float | Direct mapping |

### 10. 料金設定 → `basic_price` table
| Legacy Field | New Column | Type | Notes |
|--------------|------------|------|-------|
| - | code | varchar | Generate "DEFAULT" |
| - | title | varchar | "Default Pricing" |
| - | explanation | text | "Legacy pricing configuration" |
| [pricing data] | price_json | json | Parse and structure pricing data |

## Data Transformation Requirements

### 1. Character Encoding
- Legacy files appear to use Japanese encoding (Shift-JIS)
- Need to convert to UTF-8 for database storage

### 2. Date Format Conversion
- Legacy: `YYYY/MM/DD` format
- New: PostgreSQL `timestamptz` and `date` types
- Handle timezone conversion (assume JST)

### 3. Default Values
- `created_at`, `updated_at`: Use current timestamp
- `deleted_at`: NULL for all records
- `password`: Generate bcrypt hash of default password
- Missing required fields: Use appropriate defaults

### 4. Code Generation
- Many new tables require `code` fields not present in legacy data
- Generate codes from names/titles using slugification
- Ensure uniqueness within each table

### 5. Foreign Key Resolution
- `statutory_id` in customer table needs lookup
- `block_id` in district table needs creation/lookup
- User references need ID mapping

## Files Not Mapped to New Schema

### Files with No Direct Mapping:
1. **会社マスタ_Company Master** - Company configuration data
2. **番号管理マスタ** - Number sequence management
3. **A/B/C請負料金規定表** - Detailed pricing tables (may be incorporated into basic_price JSON)
4. **備品マスタ** - Equipment master (no equipment table in new schema)
5. **地域別出張料金一覧** - Regional travel fees (may be part of distant_fee)
6. **支払い請求書一覧BY年月氏名** - Payment/invoice history (transactional data)
7. **日報一覧作業日順** - Daily reports (transactional data)
8. **書類管理用ビュー** - Document management view
9. **給与明細一覧作業員番号順日付順** - Payslip details (transactional data)

### Recommendation:
- Archive these files for reference
- Some data may be incorporated into JSON fields or configuration tables later
- Transactional data should be migrated separately after master data

## Migration Priority Order

1. **Phase 1 - Master Data (Independent tables)**:
   - `consumption_tax`
   - `income_tax`
   - `qualification`
   - `basic_price`

2. **Phase 2 - Block/District Data**:
   - `block`
   - `district`

3. **Phase 3 - User Data**:
   - `user`
   - `user_role`

4. **Phase 4 - Customer Data**:
   - `statutory` (if needed)
   - `transfer_destination` (if needed)
   - `customer`
   - `department`
   - `department_pic`

5. **Phase 5 - Distance/Travel Data**:
   - `distant_fee`
   - `distant_travel_allowance`

## Data Validation Rules

### Required Field Validation
- All `code` fields must be unique within their table
- All `name`/`title` fields must not be empty
- Date fields must be valid dates
- Float fields must be non-negative where applicable

### Data Type Conversions
- **String to Float**: Handle Japanese number formats, empty strings as 0
- **Date Parsing**: Support `YYYY/MM/DD` format, handle invalid dates
- **Character Encoding**: Convert from Shift-JIS/CP932 to UTF-8
- **JSON Generation**: Structure pricing data into valid JSON format

### Default Value Strategy
- **Timestamps**: Use migration execution time for `created_at`/`updated_at`
- **Passwords**: Generate bcrypt hash of "password123" or similar default
- **Codes**: Generate from names using: lowercase, replace spaces with underscores, remove special chars
- **Optional Fields**: Use empty string for text fields, NULL for nullable fields

### Error Handling Strategy
- **Duplicate Codes**: Append numeric suffix (e.g., "code_1", "code_2")
- **Invalid Data**: Log warnings, use default values where possible
- **Missing Required Data**: Skip record with error log
- **Encoding Issues**: Attempt multiple encoding detection methods

## Migration Script Requirements

### CLI Interface
```bash
# Migrate single file
go run cmd/migrate/main.go --file="資格マスタ" --dry-run
go run cmd/migrate/main.go --file="資格マスタ" --execute

# Migrate all files in order
go run cmd/migrate/main.go --all --dry-run
go run cmd/migrate/main.go --all --execute
```

### Features Required
- **Dry Run Mode**: Preview changes without executing
- **Progress Reporting**: Show records processed/skipped/errors
- **Transaction Safety**: Rollback on errors
- **Logging**: Detailed logs with timestamps
- **Resume Capability**: Skip already migrated records
- **Validation**: Pre-migration data validation

### Database Integration
- Use existing domain layer patterns
- Leverage GORM for database operations
- Implement proper error handling and logging
- Support transaction management

## Next Steps

1. Create CLI migration script architecture
2. Implement migration for one data file (recommend starting with `qualification`)
3. Test and validate data integrity
4. Extend to other files following the same pattern
