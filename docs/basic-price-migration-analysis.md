# Basic Price Migration Analysis and Implementation

## Overview

The Basic Price migration has been completely re-analyzed and updated to correctly handle the time-based pricing structure from the `料金設定` (Pricing Configuration) file. The new implementation creates separate database records for each pricing block with structured JSON data representing hourly pricing information.

## Legacy Data Structure Analysis

### File Format: `nbs_master_data/料金設定`

The pricing configuration file contains three main pricing sections:
1. **１次料金** (Primary Pricing) - Standard hourly rates
2. **２次料金** (Secondary Pricing) - Special rates for specific time periods  
3. **３次料金** (Tertiary Pricing) - Additional pricing tier

Each section defines pricing for three blocks:
- **A Block** - Highest pricing tier
- **B Block** - Medium pricing tier  
- **C Block** - Lowest pricing tier

### Time Range Structure

The file defines pricing using time ranges like:
```
6:00～8:00
18:00～22:00    2250    2000    1500
8:00～18:00     1750    1500    1000
22:00～6:00     2750    2500    2000
```

Where the three numbers represent prices for A, B, and C blocks respectively.

## New Implementation Details

### Database Structure

The migration now creates **multiple records** in the `basic_price` table:

| Code | Title | Price JSON Structure |
|------|-------|---------------------|
| `primary_A` | Primary Block A Pricing | 24-hour array with hour/price objects |
| `primary_B` | Primary Block B Pricing | 24-hour array with hour/price objects |
| `primary_C` | Primary Block C Pricing | 24-hour array with hour/price objects |
| `secondary_A` | Secondary Block A Pricing | 24-hour array with hour/price objects |
| `secondary_B` | Secondary Block B Pricing | 24-hour array with hour/price objects |
| `secondary_C` | Secondary Block C Pricing | 24-hour array with hour/price objects |
| `tertiary_A` | Tertiary Block A Pricing | 24-hour array with hour/price objects |
| `tertiary_B` | Tertiary Block B Pricing | 24-hour array with hour/price objects |
| `tertiary_C` | Tertiary Block C Pricing | 24-hour array with hour/price objects |

### JSON Structure

Each `price_json` field contains an array of 24 objects (one for each hour):

```json
[
  {"hour": "00:00", "price": 2750},
  {"hour": "01:00", "price": 2750},
  {"hour": "02:00", "price": 2750},
  ...
  {"hour": "06:00", "price": 2250},
  {"hour": "07:00", "price": 2250},
  {"hour": "08:00", "price": 1750},
  ...
  {"hour": "23:00", "price": 2750}
]
```

This format matches exactly what the admin interface expects (as seen in `server/admin/report/basic_price.go`).

## Implementation Components

### 1. Updated Parser (`BasicPriceParser`)

**Key Methods:**
- `parsePricingBlocks()` - Main parsing method that creates separate records for each block
- `extractPricingSections()` - Extracts pricing sections and time ranges from file
- `convertToHourlyPricing()` - Converts time ranges to 24-hour pricing arrays
- `getPriceForHour()` - Determines price for specific hour based on time ranges
- `isHourInRange()` - Handles time range logic including overnight ranges

**Features:**
- Handles Japanese text encoding (Shift-JIS to UTF-8)
- Parses complex time range patterns with regex
- Supports overnight time ranges (e.g., 22:00-6:00)
- Creates structured hourly pricing data

### 2. Updated Migrator (`BasicPriceMigrator`)

**Key Features:**
- Creates separate database records for each pricing block
- Generates unique codes for each block (e.g., `primary_A`, `secondary_B`)
- Validates JSON structure before insertion
- Handles code uniqueness and collision detection
- Provides detailed error reporting

### 3. Validation and Testing

**Pre-Migration Validation:**
- Checks for duplicate block codes in input data
- Verifies no existing records with same codes in database
- Validates table existence

**Post-Migration Validation:**
- Counts inserted records
- Validates JSON structure
- Checks for expected pricing block codes

## Usage Examples

### Test the Migration

```bash
# Build the migration tool
go build -o bin/migrate cmd/migrate/main.go

# Test with dry run
./bin/migrate --file="料金設定" --dry-run --verbose

# Execute migration
./bin/migrate --file="料金設定" --execute --verbose

# Run automated test
./scripts/test-basic-price-migration.sh
```

### Validate Results

```bash
# Run validation queries
psql -U <user> -d <database> -f scripts/validate-migration.sql
```

Expected results:
- 9 records in `basic_price` table (3 sections × 3 blocks)
- Each record has valid JSON with 24 hourly entries
- Codes follow pattern: `primary_A`, `primary_B`, `primary_C`, etc.

## Data Transformation Logic

### Time Range Processing

1. **Parse Time Ranges**: Extract patterns like `6:00～8:00` from file
2. **Extract Prices**: Get A, B, C block prices for each time range
3. **Convert to Hours**: Map each hour (0-23) to appropriate price based on ranges
4. **Handle Overlaps**: Later ranges override earlier ones for same hours
5. **Overnight Ranges**: Handle ranges that cross midnight (e.g., 22:00-6:00)

### JSON Generation

1. **Create 24-Hour Array**: One entry for each hour of the day
2. **Format Hours**: Use "HH:00" format (e.g., "00:00", "01:00")
3. **Apply Prices**: Set price based on time range mapping
4. **Default Values**: Use 0 for hours not covered by any range

## Error Handling

### Common Issues and Solutions

1. **Encoding Problems**: File uses Shift-JIS encoding - handled by decoder
2. **Time Range Parsing**: Complex regex patterns handle various formats
3. **Overnight Ranges**: Special logic for ranges crossing midnight
4. **Missing Data**: Default to 0 price for unmapped hours
5. **Duplicate Codes**: Automatic suffix generation for uniqueness

### Validation Checks

- JSON structure validation before database insertion
- Time range overlap detection
- Price value validation (non-negative numbers)
- Code uniqueness enforcement

## Benefits of New Implementation

1. **Correct Data Structure**: Matches admin interface expectations exactly
2. **Granular Control**: Separate records for each pricing block
3. **Hourly Precision**: 24-hour pricing arrays for maximum flexibility
4. **Extensible**: Easy to add new pricing sections or blocks
5. **Validated**: Comprehensive validation and error handling
6. **Testable**: Dedicated test scripts and validation queries

## Migration Results

After successful migration, you will have:
- **9 basic_price records** (3 sections × 3 blocks)
- **Structured JSON pricing data** compatible with admin interface
- **Unique block codes** for easy identification
- **Complete hourly coverage** (24 hours per block)
- **Proper timestamps** and metadata

The migrated data will be immediately usable in the admin interface for pricing management and calculations.
