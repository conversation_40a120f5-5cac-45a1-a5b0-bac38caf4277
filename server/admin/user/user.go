package user

import (
	"errors"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"
	"golang.org/x/crypto/bcrypt"

	"strconv"
	"time"

	form2 "github.com/GoAdminGroup/go-admin/plugins/admin/modules/form"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
	"gorm.io/gorm"
)

// ---------------- Helper functions for role data ----------------

type role struct {
	ID   int
	Name string
}

// userRoleN<PERSON> returns the role name for a given user ID.
func userRoleName(userID interface{}) string {
	db := dbmanager.Manager()
	if db == nil {
		return ""
	}
	var name string
	db.Table("role").Select("name").Joins("JOIN user_role ur ON ur.role_id = role.id").Where("ur.user_id = ?", userID).Scan(&name)
	return name
}

// roleOptions builds FieldOptions for the role dropdown, selecting the current role if editing.
func roleOptions(userID string) types.FieldOptions {
	db := dbmanager.Manager()
	if db == nil {
		return nil
	}
	var roles []role
	db.Table("role").Select("id, name").Where("deleted_at IS NULL").Find(&roles)
	var currentRoleID int
	if userID != "" {
		db.Table("user_role").Select("role_id").Where("user_id = ?", userID).Scan(&currentRoleID)
	}
	opts := make(types.FieldOptions, 0, len(roles))
	for _, r := range roles {
		opts = append(opts, types.FieldOption{
			Text:     r.Name,
			Value:    strconv.Itoa(r.ID),
			Selected: r.ID == currentRoleID,
		})
	}
	return opts
}

// hourlyWageOptions builds FieldOptions for the hourly wage dropdown, selecting the current hourly wage if editing.
func hourlyWageOptions() types.FieldOptions {
	db := dbmanager.Manager()
	if db == nil {
		return nil
	}
	var hourlyWages []struct {
		ID    int
		Title string
	}
	db.Table("hourly_wage").Select("id, title").Where("deleted_at IS NULL").Find(&hourlyWages)
	opts := make(types.FieldOptions, 0, len(hourlyWages))
	for _, h := range hourlyWages {
		opts = append(opts, types.FieldOption{
			Text:  h.Title,
			Value: strconv.Itoa(h.ID),
		})
	}
	return opts
}

// overtimeAllowanceOptions builds FieldOptions for the overtime allowance dropdown, selecting the current overtime allowance if editing.
func overtimeAllowanceOptions() types.FieldOptions {
	db := dbmanager.Manager()
	if db == nil {
		return nil
	}
	var overtimeAllowances []struct {
		ID    int
		Title string
	}
	db.Table("overtime_allowance").Select("id, title").Where("deleted_at IS NULL").Find(&overtimeAllowances)
	opts := make(types.FieldOptions, 0, len(overtimeAllowances))
	for _, o := range overtimeAllowances {
		opts = append(opts, types.FieldOption{
			Text:  o.Title,
			Value: strconv.Itoa(o.ID),
		})
	}
	return opts
}

// ----------------------------------------------------------------

func GetUserTable(ctx *context.Context) table.Table {
	// config the table model.
	userTable := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := userTable.GetInfo()

	// set id sortable.
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("ユーザー名", "username", db.Varchar)
	info.AddField("氏名", "name", db.Varchar)

	// Add role field with join to role table
	info.AddField("役割", "name", db.Varchar).
		FieldDisplay(func(val types.FieldModel) interface{} {
			return userRoleName(val.Row["id"])
		}).
		FieldJoin(types.Join{
			Table:     "user_role",
			Field:     "id", // user.id = user_role.user_id
			JoinField: "user_id",
		}).
		FieldJoin(types.Join{
			Table:     "role",
			Field:     "role_id", // user_role.role_id = role.id
			JoinField: "id",
			BaseTable: "user_role",
		})

	// Add dependent field
	info.AddField("所得税", "dependent", db.Varchar).
		FieldDisplay(func(val types.FieldModel) interface{} {
			if val.Value == "" {
				return "未設定"
			}

			switch val.Value {
			case constanta.TaxDeduction:
				return "控除非対称"
			case constanta.TaxDependentSingle:
				return "乙"
			case constanta.TaxDependent0:
				return "所得税0人"
			case constanta.TaxDependent1:
				return "所得税1人"
			case constanta.TaxDependent2:
				return "所得税2人"
			case constanta.TaxDependent3:
				return "所得税3人"
			case constanta.TaxDependent4:
				return "所得税4人"
			case constanta.TaxDependent5:
				return "所得税5人"
			case constanta.TaxDependent6:
				return "所得税6人"
			case constanta.TaxDependent7:
				return "所得税7人"
			default:
				return val.Value
			}
		})

	// Add hourly wage field with join to hourly_wage table
	info.AddField("賃金", "hourly_wage_id", db.Bigint).
		FieldDisplay(func(val types.FieldModel) interface{} {
			gormDB := dbmanager.Manager()
			var hourlyWageTitle string
			if gormDB != nil {
				_ = gormDB.Table("hourly_wage").Select("title").Where("id = ?", val.Value).Scan(&hourlyWageTitle).Error
			}
			if hourlyWageTitle == "" {
				return val.Value
			}
			return hourlyWageTitle
		})

	// Add overtime allowance field with join to overtime_allowance table
	info.AddField("加算手当", "overtime_allowance_id", db.Bigint).
		FieldDisplay(func(val types.FieldModel) interface{} {
			gormDB := dbmanager.Manager()
			var overtimeAllowanceTitle string
			if gormDB != nil {
				_ = gormDB.Table("overtime_allowance").Select("title").Where("id = ?", val.Value).Scan(&overtimeAllowanceTitle).Error
			}
			if overtimeAllowanceTitle == "" {
				return val.Value
			}
			return overtimeAllowanceTitle
		})

	info.AddField("作成日", "created_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})
	info.AddField("更新日", "updated_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})

	// set the title and description of table page.
	info.SetTable("user").SetTitle("ユーザー")

	// Soft delete: mark deleted_at instead of hard delete
	info.SetDeleteFn(func(ids []string) error {
		gormDB := dbmanager.Manager()
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("user").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude soft-deleted records
	info.WhereRaw("\"user\".deleted_at IS NULL")

	// Customize detail panel role display
	detailPanel := userTable.GetDetail()
	for idx, field := range detailPanel.FieldList {
		if field.Head == "役割" {
			detailPanel.FieldList[idx].FieldDisplay.Display = func(val types.FieldModel) interface{} {
				return userRoleName(val.Row["id"])
			}
			break
		}
	}

	formList := userTable.GetForm()

	// set id editable is false.
	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("ユーザー名", "username", db.Varchar, form.Text).FieldMust()
	formList.AddField("氏名", "name", db.Varchar, form.Text).FieldMust()

	// Add role field as select dropdown - references role table
	formList.AddField("役割", "role_id", db.Int, form.SelectSingle).
		FieldDisplay(func(val types.FieldModel) interface{} {
			if val.Value != "" && val.Value != val.Row["name"] {
				return val.Value
			}
			return userRoleName(val.Row["id"])
		}).
		FieldOptionInitFn(func(val types.FieldModel) types.FieldOptions {
			return roleOptions(val.ID)
		}).
		FieldMust().
		FieldHelpMsg("Select a role for this user (one role per user)").
		SetPostValidator(func(values form2.Values) error {
			if values.Get("role_id") == "" {
				return errors.New("役割は必須です")
			}
			return nil
		})

	// Add dependent field as select dropdown for tax dependency types
	formList.AddField("所得税", "dependent", db.Varchar, form.SelectSingle).
		FieldOptions(types.FieldOptions{
			{Text: "未設定", Value: ""},
			{Text: "控除非対称", Value: constanta.TaxDeduction},
			{Text: "乙", Value: constanta.TaxDependentSingle},
			{Text: "所得税0人", Value: constanta.TaxDependent0},
			{Text: "所得税1人", Value: constanta.TaxDependent1},
			{Text: "所得税2人", Value: constanta.TaxDependent2},
			{Text: "所得税3人", Value: constanta.TaxDependent3},
			{Text: "所得税4人", Value: constanta.TaxDependent4},
			{Text: "所得税5人", Value: constanta.TaxDependent5},
			{Text: "所得税6人", Value: constanta.TaxDependent6},
			{Text: "所得税7人", Value: constanta.TaxDependent7},
		}).
		FieldMust().
		FieldHelpMsg("Select tax dependency type for income tax calculation").
		SetPostValidator(func(values form2.Values) error {
			if values.Get("dependent") == "" {
				return errors.New("所得税は必須です")
			}
			return nil
		})

	// Add hourly wage field as select dropdown - references hourly_wage table
	formList.AddField("賃金", "hourly_wage_id", db.Bigint, form.SelectSingle).
		FieldOptions(hourlyWageOptions()).
		FieldMust().
		FieldHelpMsg("Select hourly wage for this user").
		SetPostValidator(func(values form2.Values) error {
			if values.Get("hourly_wage_id") == "" {
				return errors.New("賃金は必須です")
			}
			return nil
		})

	// Add overtime allowance field as select dropdown - references overtime_allowance table
	formList.AddField("加算手当", "overtime_allowance_id", db.Bigint, form.SelectSingle).
		FieldOptions(overtimeAllowanceOptions()).
		FieldMust().
		FieldHelpMsg("Select overtime allowance for this user").
		SetPostValidator(func(values form2.Values) error {
			if values.Get("overtime_allowance_id") == "" {
				return errors.New("加算手当は必須です")
			}
			return nil
		})

	// Add password change field (allow add and edit)
	formList.AddField("パスワード", "password", db.Varchar, form.Password).
		FieldDisplay(func(val types.FieldModel) interface{} {
			// Always show empty password field in the form, even when editing
			return ""
		}).
		FieldHelpMsg("Enter password for new user, or leave blank to keep current password for existing user").
		FieldPostFilterFn(func(value types.PostFieldModel) interface{} {
			// Only process if a password was provided
			if len(value.Value) == 0 || value.Value[0] == "" {
				// Try to use the existing hash from the row (may be omitted by GoAdmin for security)
				if hash, ok := value.Row["password"]; ok && hash != "" {
					return hash
				}
				// Fallback: fetch from DB so we don't set NULL
				gormDB := dbmanager.Manager()
				if gormDB != nil && value.ID != "" {
					var currentPwd string
					_ = gormDB.Table("user").Select("password").Where("id = ?", value.ID).Scan(&currentPwd)
					return currentPwd
				}
				return nil
			}

			newPassword := value.Value[0]

			// Hash the password using bcrypt
			hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
			if err != nil {
				return nil // Don't update on error
			}

			// Return the hashed password so GoAdmin's update query sets it.
			return string(hashedPassword)
		})

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert(). // set only on insert
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

		// Updated At auto timestamp
	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow(). // always set to current time
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	// set the title and description of form page.
	formList.SetTable("user").SetTitle("ユーザー")

	// Custom insert: handle role junction insert
	formList.SetInsertFn(func(values form2.Values) error {
		gormDB := dbmanager.Manager()
		if gormDB == nil {
			return nil
		}

		username := values.Get("username")
		name := values.Get("name")
		password := values.Get("password") // already hashed by PostFilterFn
		roleID := values.Get("role_id")
		dependent := values.Get("dependent")
		hourlyWageID := values.Get("hourly_wage_id")
		overtimeAllowanceID := values.Get("overtime_allowance_id")

		// Insert user
		var userID int64
		err := gormDB.Transaction(func(tx *gorm.DB) error {
			// create user record and fetch id
			if err := tx.Raw("INSERT INTO \"user\" (username, password, name, dependent, hourly_wage_id, overtime_allowance_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id", username, password, name, dependent, hourlyWageID, overtimeAllowanceID, time.Now(), time.Now()).Scan(&userID).Error; err != nil {
				return err
			}

			if roleID != "" {
				if err := tx.Exec("INSERT INTO user_role (user_id, role_id) VALUES (?, ?)", userID, roleID).Error; err != nil {
					return err
				}
			}
			return nil
		})
		return err
	})

	// PostHook for updating role on edit
	formList.SetPostHook(func(values form2.Values) error {
		if values.IsInsertPost() {
			return nil // handled in InsertFn
		}
		gormDB := dbmanager.Manager()
		if gormDB == nil {
			return nil
		}
		userID := values.Get("id")
		roleID := values.Get("role_id")
		if userID == "" || roleID == "" {
			return nil
		}

		// check if mapping exists
		var cnt int64
		if err := gormDB.Table("user_role").Where("user_id = ?", userID).Count(&cnt).Error; err != nil {
			return err
		}
		if cnt > 0 {
			return gormDB.Table("user_role").Where("user_id = ?", userID).Update("role_id", roleID).Error
		}
		return gormDB.Exec("INSERT INTO user_role (user_id, role_id) VALUES (?, ?)", userID, roleID).Error
	})

	return userTable
}
