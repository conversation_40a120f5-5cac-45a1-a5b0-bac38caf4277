package report

import (
	"encoding/json"
	"fmt"
	"html/template"
	"strconv"
	"strings"
	"time"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	form2 "github.com/GoAdminGroup/go-admin/plugins/admin/modules/form"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
)

// GetHourlyWageTable configures GoAdmin CRUD for the hourly_wage table.
func GetHourlyWageTable(ctx *context.Context) table.Table {
	tbl := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := tbl.GetInfo()
	gormDB := dbmanager.Manager()

	// List fields
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("基本料金コード", "code", db.Varchar).
		FieldDisplay(func(m types.FieldModel) interface{} {
			return template.HTML(fmt.Sprintf(`<span style="white-space:nowrap;min-width:120px;display:inline-block;">%s</span>`, m.Value))
		})
	info.AddField("表示タイトル", "title", db.Varchar).
		FieldDisplay(func(m types.FieldModel) interface{} {
			return template.HTML(fmt.Sprintf(`<span style="white-space:nowrap;min-width:200px;display:inline-block;">%s</span>`, m.Value))
		})
	info.AddField("説明", "explanation", db.Text).
		FieldDisplay(func(m types.FieldModel) interface{} {
			return template.HTML(fmt.Sprintf(`<span style="white-space:wrap;max-width:800px;display:inline-block;">%s</span>`, m.Value))
		})

	// Display price_json horizontally
	info.AddField("価格設定", "price_json", db.JSON).FieldDisplay(func(model types.FieldModel) interface{} {
		if model.Value == "" {
			return "No pricing data"
		}

		var pricingData []map[string]interface{}
		if err := json.Unmarshal([]byte(model.Value), &pricingData); err != nil {
			return "Error parsing pricing data"
		}

		headerRow := "<tr>"
		priceRow := "<tr>"
		for _, item := range pricingData {
			hour := fmt.Sprintf("%v", item["hour"])
			var price string
			switch v := item["price"].(type) {
			case float64:
				price = fmt.Sprintf("%.0f", v)
			case string:
				price = v
			default:
				price = fmt.Sprintf("%v", v)
			}
			headerRow += fmt.Sprintf(`<th style="border:1px solid #ddd;padding:8px;text-align:left;">%s</th>`, hour)
			priceRow += fmt.Sprintf(`<td style="border:1px solid #ddd;padding:8px;">%s</td>`, price)
		}
		headerRow += "</tr>"
		priceRow += "</tr>"

		tableContent := fmt.Sprintf(`<div style="overflow-x:auto;">
            <table style="min-width:1200px;border-collapse:collapse;margin:10px 0;white-space:nowrap;">
            <thead>%s</thead>
            <tbody>%s</tbody>
        </table>
        </div>`, headerRow, priceRow)

		return template.HTML(tableContent)
	})

	info.AddField("作成日", "created_at", db.Timestamp).
		FieldDisplay(func(m types.FieldModel) interface{} {
			value := parse.AdminStrTimestamp(m.Value)
			return template.HTML(fmt.Sprintf(`<span style="white-space:nowrap;min-width:140px;display:inline-block;">%s</span>`, value))
		})
	info.AddField("更新日", "updated_at", db.Timestamp).
		FieldDisplay(func(m types.FieldModel) interface{} {
			value := parse.AdminStrTimestamp(m.Value)
			return template.HTML(fmt.Sprintf(`<span style="white-space:nowrap;min-width:140px;display:inline-block;">%s</span>`, value))
		})

	info.SetTable("hourly_wage").SetTitle("賃金")

	// Soft delete implementation
	info.SetDeleteFn(func(ids []string) error {
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("hourly_wage").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude soft-deleted records
	info.WhereRaw("\"hourly_wage\".deleted_at IS NULL")

	// Form configuration
	formList := tbl.GetForm()

	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("基本料金コード", "code", db.Varchar, form.Text).FieldMust()
	formList.AddField("表示タイトル", "title", db.Varchar, form.Text).FieldMust()
	formList.AddField("説明", "explanation", db.Text, form.RichText)

	// Hidden JSON column to store pricing data
	formList.AddField("価格設定", "price_json", db.JSON, form.Text).
		FieldHide().
		FieldDefault("[]")

	// Add hourly price input fields from 00:00 to 23:00.
	for i := 0; i < 24; i++ {
		hour := fmt.Sprintf("%02d:00", i)
		col := fmt.Sprintf("price_%02d", i)
		h := hour // capture loop variable
		formList.AddField(hour, col, db.Decimal, form.Text).
			FieldDisplay(func(val types.FieldModel) interface{} {
				if raw, ok := val.Row["price_json"]; ok && raw != nil {
					var pricing []map[string]interface{}
					switch v := raw.(type) {
					case string:
						_ = json.Unmarshal([]byte(v), &pricing)
					case []byte:
						_ = json.Unmarshal(v, &pricing)
					}
					for _, item := range pricing {
						if itemHour, _ := item["hour"].(string); itemHour == h {
							switch p := item["price"].(type) {
							case float64:
								return fmt.Sprintf("%.0f", p)
							case string:
								return p
							default:
								return fmt.Sprintf("%v", p)
							}
						}
					}
				}
				return ""
			}).
			FieldOptionExt(map[string]interface{}{
				"style": "width: 100px",
				"class": "price-input",
			})
	}

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.SetTable("hourly_wage").SetTitle("賃金")

	// Build price_json from hourly price inputs before saving.
	formList.SetPreProcessFn(func(values form2.Values) form2.Values {
		var pricing []map[string]interface{}
		for i := 0; i < 24; i++ {
			hour := fmt.Sprintf("%02d:00", i)
			key := fmt.Sprintf("price_%02d", i)
			priceStr := values.Get(key)
			// Remove the custom field so it won't attempt to insert into DB.
			delete(values, key)

			if priceStr == "" {
				priceStr = "0"
			}
			// Handle both comma and dot as decimal separator
			priceStr = strings.Replace(priceStr, ",", ".", 1)
			price, _ := strconv.ParseFloat(priceStr, 64)
			pricing = append(pricing, map[string]interface{}{"hour": hour, "price": price})
		}

		// Convert pricing data to JSON string
		b, err := json.Marshal(pricing)
		if err != nil {
			fmt.Printf("Error marshaling price data: %v\n", err)
			return values
		}

		// Set the price_json field
		values.Add("price_json", string(b))
		return values
	})

	return tbl
}
