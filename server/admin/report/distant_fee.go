package report

import (
	"time"

	"github.com/GoAdminGroup/go-admin/context"
	"github.com/GoAdminGroup/go-admin/modules/db"
	"github.com/GoAdminGroup/go-admin/plugins/admin/modules/table"
	"github.com/GoAdminGroup/go-admin/template/types"
	"github.com/GoAdminGroup/go-admin/template/types/form"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
)

// GetDistantFeeTable configures GoAdmin CRUD for the distant_fee table.
func GetDistantFeeTable(ctx *context.Context) table.Table {
	tbl := table.NewDefaultTable(ctx, table.Config{
		Driver:     db.DriverPostgresql,
		CanAdd:     true,
		Editable:   true,
		Deletable:  true,
		Exportable: true,
		Connection: table.DefaultConnectionName,
		PrimaryKey: table.PrimaryKey{
			Type: db.Bigint,
			Name: table.DefaultPrimaryKeyName,
		},
	})

	info := tbl.GetInfo()
	gormDB := dbmanager.Manager()

	// List fields
	info.AddField("ID", "id", db.Bigint).FieldSortable()
	info.AddField("加算額コード", "code", db.Varchar)
	info.AddField("表示タイトル", "title", db.Varchar)
	info.AddField("説明", "explanation", db.Text)
	info.AddField("ユニット", "unit", db.Float).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.StrFloatToStrInt(m.Value)
	})
	info.AddField("加算額(1時間あたり)", "add_amount_per_hour", db.Float).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.StrFloatToStrInt(m.Value)
	})
	info.AddField("作成日", "created_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})
	info.AddField("更新日", "updated_at", db.Timestamp).FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.AdminStrTimestamp(m.Value)
	})

	info.SetTable("distant_fee").SetTitle("遠方料金")

	// Soft delete implementation
	info.SetDeleteFn(func(ids []string) error {
		if gormDB == nil {
			return nil
		}
		return gormDB.Table("distant_fee").Where("id IN ?", ids).Updates(map[string]interface{}{"deleted_at": time.Now()}).Error
	})

	// Exclude soft-deleted records
	info.WhereRaw("\"distant_fee\".deleted_at IS NULL")

	// Form configuration
	formList := tbl.GetForm()

	formList.AddField("ID", "id", db.Bigint, form.Default).FieldNotAllowEdit()
	formList.AddField("加算額コード", "code", db.Varchar, form.Text).FieldMust()
	formList.AddField("表示タイトル", "title", db.Varchar, form.Text).FieldMust()
	formList.AddField("説明", "explanation", db.Text, form.RichText)
	formList.AddField("ユニット", "unit", db.Float, form.Number).FieldMust().FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.StrFloatToStrInt(m.Value)
	})
	formList.AddField("加算額(1時間あたり)", "add_amount_per_hour", db.Float, form.Number).FieldMust().FieldDisplay(func(m types.FieldModel) interface{} {
		return parse.StrFloatToStrInt(m.Value)
	})

	formList.AddField("作成日", "created_at", db.Timestamp, form.Datetime).
		FieldNowWhenInsert().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.AddField("更新日", "updated_at", db.Timestamp, form.Datetime).
		FieldNow().
		FieldHideWhenCreate().
		FieldHideWhenUpdate().
		FieldNotAllowEdit().
		FieldDisplay(func(field types.FieldModel) interface{} {
			return parse.AdminStrTimestamp(field.Value)
		})

	formList.SetTable("distant_fee").SetTitle("遠方料金")

	return tbl
}
