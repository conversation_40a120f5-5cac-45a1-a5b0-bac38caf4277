package constanta

const (
	Success      = "Success"
	Unauthorized = "無許可"

	EmptyAuth         = "認証が空"
	InvalidAuth       = "認証が無効"
	UnexpectedSigning = "不適切な署名方法: %v"
	UserInexist       = "ユーザーが存在しません"

	ServiceRunning = "service is running"
	ServiceBroken  = "service broken"

	HeaderKeyContentType = "Content-Type"

	EnvDevelopment = "development"
)

const (
	RoleWorker     = "worker"
	RoleSupervisor = "supervisor"
	RoleSubAdmin   = "sub admin"
	RoleAdmin      = "admin"
	RoleSuperAdmin = "super admin"
)

// Tax dependency types for income tax calculation
const (
	TaxDeduction       = "ta_deduction"
	TaxDependentSingle = "ta_single"
	TaxDependent0      = "ta_0"
	TaxDependent1      = "ta_1"
	TaxDependent2      = "ta_2"
	TaxDependent3      = "ta_3"
	TaxDependent4      = "ta_4"
	TaxDependent5      = "ta_5"
	TaxDependent6      = "ta_6"
	TaxDependent7      = "ta_7"
)
