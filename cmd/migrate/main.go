package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/config"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/migration"
)

func main() {
	var (
		file    = flag.String("file", "", "Specific legacy data file to migrate (e.g., '資格マスタ')")
		all     = flag.Bool("all", false, "Migrate all files in recommended order")
		dryRun  = flag.Bool("dry-run", false, "Preview changes without executing")
		execute = flag.Bool("execute", false, "Execute the migration")
		verbose = flag.Bool("verbose", false, "Enable verbose logging")
		dataDir = flag.String("data-dir", "nbs_master_data", "Directory containing legacy data files")
	)
	flag.Parse()

	// Validate flags
	if !*dryRun && !*execute {
		fmt.Println("Error: Must specify either --dry-run or --execute")
		flag.Usage()
		os.Exit(1)
	}

	if *file == "" && !*all {
		fmt.Println("Error: Must specify either --file or --all")
		flag.Usage()
		os.Exit(1)
	}

	if *file != "" && *all {
		fmt.Println("Error: Cannot specify both --file and --all")
		flag.Usage()
		os.Exit(1)
	}

	// Initialize database connection
	config.InitDatabase()

	// Initialize migration service
	migrationService := migration.NewMigrationService(&migration.Config{
		DataDir: *dataDir,
		DryRun:  *dryRun,
		Verbose: *verbose,
	})

	ctx := context.Background()

	if *all {
		// Migrate all files in recommended order
		err := migrationService.MigrateAll(ctx)
		if err != nil {
			log.Fatalf("Migration failed: %v", err)
		}
	} else {
		// Migrate specific file
		err := migrationService.MigrateFile(ctx, *file)
		if err != nil {
			log.Fatalf("Migration failed: %v", err)
		}
	}

	fmt.Println("Migration completed successfully!")
}
