#!/bin/bash

# Test script for basic price migration
# This script tests the migration of pricing configuration data

set -e

echo "=== Basic Price Migration Test Script ==="
echo

# Check if the migration binary exists
if [ ! -f "bin/migrate" ]; then
    echo "Building migration binary..."
    go build -o bin/migrate cmd/migrate/main.go
    if [ $? -eq 0 ]; then
        echo "   ✓ Migration binary built successfully"
    else
        echo "   ✗ Failed to build migration binary"
        exit 1
    fi
fi

# Check if the data file exists
DATA_FILE="nbs_master_data/料金設定"
if [ -f "$DATA_FILE" ]; then
    echo "   ✓ Data file found: $DATA_FILE"
else
    echo "   ✗ Data file not found: $DATA_FILE"
    echo "   Please ensure the legacy data files are in the nbs_master_data/ directory"
    exit 1
fi

echo

# Test dry run mode
echo "1. Testing basic price migration dry run..."
./bin/migrate --file="料金設定" --dry-run --verbose
if [ $? -eq 0 ]; then
    echo "   ✓ Dry run completed successfully"
else
    echo "   ✗ Dry run failed"
    exit 1
fi

echo

# Ask user if they want to proceed with actual migration
echo "2. Ready to test actual migration"
read -p "   Do you want to proceed with the actual migration? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "   Running actual migration..."
    ./bin/migrate --file="料金設定" --execute --verbose
    if [ $? -eq 0 ]; then
        echo "   ✓ Migration completed successfully"
        
        echo
        echo "3. Validating migration results..."
        echo "   You can now check the database for the following records:"
        echo "   - primary_A (Primary pricing for A block)"
        echo "   - primary_B (Primary pricing for B block)" 
        echo "   - primary_C (Primary pricing for C block)"
        echo "   - secondary_A (Secondary pricing for A block)"
        echo "   - secondary_B (Secondary pricing for B block)"
        echo "   - secondary_C (Secondary pricing for C block)"
        echo "   - tertiary_A (Tertiary pricing for A block)"
        echo "   - tertiary_B (Tertiary pricing for B block)"
        echo "   - tertiary_C (Tertiary pricing for C block)"
        echo
        echo "   Each record should contain a price_json field with 24 hourly pricing entries."
    else
        echo "   ✗ Migration failed"
        exit 1
    fi
else
    echo "   Migration skipped by user"
fi

echo

echo "=== Test completed ==="
