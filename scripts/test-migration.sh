#!/bin/bash

# Test script for data migration
# This script tests the migration of qualification data

set -e

echo "=== Data Migration Test Script ==="
echo

# Check if the migration binary can be built
echo "1. Building migration binary..."
go build -o bin/migrate cmd/migrate/main.go
if [ $? -eq 0 ]; then
    echo "   ✓ Migration binary built successfully"
else
    echo "   ✗ Failed to build migration binary"
    exit 1
fi

# Check if the data file exists
DATA_FILE="nbs_master_data/資格マスタ"
if [ -f "$DATA_FILE" ]; then
    echo "   ✓ Data file found: $DATA_FILE"
else
    echo "   ✗ Data file not found: $DATA_FILE"
    echo "   Please ensure the legacy data files are in the nbs_master_data/ directory"
    exit 1
fi

echo

# Test dry run mode
echo "2. Testing dry run mode..."
./bin/migrate --file="資格マスタ" --dry-run --verbose
if [ $? -eq 0 ]; then
    echo "   ✓ Dry run completed successfully"
else
    echo "   ✗ Dry run failed"
    exit 1
fi

echo

# Ask user if they want to proceed with actual migration
echo "3. Ready to test actual migration"
read -p "   Do you want to proceed with the actual migration? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "   Running actual migration..."
    ./bin/migrate --file="資格マスタ" --execute --verbose
    if [ $? -eq 0 ]; then
        echo "   ✓ Migration completed successfully"
    else
        echo "   ✗ Migration failed"
        exit 1
    fi
else
    echo "   Migration skipped by user"
fi

echo

# Cleanup
echo "4. Cleaning up..."
rm -f bin/migrate
echo "   ✓ Cleanup completed"

echo
echo "=== Test completed ==="
