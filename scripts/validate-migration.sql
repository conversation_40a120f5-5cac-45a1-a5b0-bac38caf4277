-- Validation queries for data migration
-- Run these queries after migration to verify data integrity

-- 1. Check qualification table
SELECT 
    'qualification' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted_at IS NULL THEN 1 END) as active_records,
    MIN(created_at) as earliest_created,
    MAX(updated_at) as latest_updated
FROM qualification;

-- Sample qualification records
SELECT 
    code, 
    title, 
    add_claim, 
    paid_amount,
    created_at
FROM qualification 
WHERE deleted_at IS NULL 
ORDER BY created_at 
LIMIT 5;

-- Check for duplicate codes
SELECT 
    code, 
    COUNT(*) as count
FROM qualification 
WHERE deleted_at IS NULL
GROUP BY code 
HAVING COUNT(*) > 1;

-- Check for missing required fields
SELECT 
    'Missing codes' as issue,
    COUNT(*) as count
FROM qualification 
WHERE code IS NULL OR code = '';

SELECT 
    'Missing titles' as issue,
    COUNT(*) as count
FROM qualification 
WHERE title IS NULL OR title = '';

-- Check data ranges
SELECT 
    'add_claim_stats' as metric,
    MIN(add_claim) as min_value,
    MAX(add_claim) as max_value,
    AVG(add_claim) as avg_value
FROM qualification 
WHERE deleted_at IS NULL;

SELECT 
    'paid_amount_stats' as metric,
    MIN(paid_amount) as min_value,
    MAX(paid_amount) as max_value,
    AVG(paid_amount) as avg_value
FROM qualification 
WHERE deleted_at IS NULL;

-- Check for potential encoding issues (Japanese characters)
SELECT 
    code,
    title,
    LENGTH(title) as title_length,
    CASE 
        WHEN title ~ '[^\x00-\x7F]' THEN 'Contains non-ASCII'
        ELSE 'ASCII only'
    END as encoding_check
FROM qualification 
WHERE deleted_at IS NULL
ORDER BY title_length DESC
LIMIT 10;

-- Basic Price Migration Validation
SELECT
    'basic_price' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted_at IS NULL THEN 1 END) as active_records,
    MIN(created_at) as earliest_created,
    MAX(updated_at) as latest_updated
FROM basic_price;

-- Sample basic price records
SELECT
    code,
    title,
    LENGTH(price_json) as json_length,
    created_at
FROM basic_price
WHERE deleted_at IS NULL
ORDER BY created_at
LIMIT 10;

-- Check for duplicate codes in basic_price
SELECT
    code,
    COUNT(*) as count
FROM basic_price
WHERE deleted_at IS NULL
GROUP BY code
HAVING COUNT(*) > 1;

-- Validate JSON structure in basic_price
SELECT
    code,
    title,
    CASE
        WHEN price_json::json IS NOT NULL THEN 'Valid JSON'
        ELSE 'Invalid JSON'
    END as json_validation,
    json_array_length(price_json::json) as hourly_entries_count
FROM basic_price
WHERE deleted_at IS NULL
ORDER BY code;

-- Check for expected pricing block codes
SELECT
    'Expected pricing blocks' as check_type,
    CASE
        WHEN EXISTS (SELECT 1 FROM basic_price WHERE code LIKE 'primary_%' AND deleted_at IS NULL) THEN 'Found'
        ELSE 'Missing'
    END as primary_blocks,
    CASE
        WHEN EXISTS (SELECT 1 FROM basic_price WHERE code LIKE 'secondary_%' AND deleted_at IS NULL) THEN 'Found'
        ELSE 'Missing'
    END as secondary_blocks,
    CASE
        WHEN EXISTS (SELECT 1 FROM basic_price WHERE code LIKE 'tertiary_%' AND deleted_at IS NULL) THEN 'Found'
        ELSE 'Missing'
    END as tertiary_blocks;

-- Summary report
SELECT
    'MIGRATION VALIDATION SUMMARY' as report_type,
    (SELECT COUNT(*) FROM qualification WHERE deleted_at IS NULL) as total_qualifications,
    (SELECT COUNT(DISTINCT code) FROM qualification WHERE deleted_at IS NULL) as unique_qualification_codes,
    (SELECT COUNT(*) FROM basic_price WHERE deleted_at IS NULL) as total_basic_prices,
    (SELECT COUNT(DISTINCT code) FROM basic_price WHERE deleted_at IS NULL) as unique_basic_price_codes,
    CASE
        WHEN (SELECT COUNT(*) FROM qualification WHERE deleted_at IS NULL) > 0
         AND (SELECT COUNT(*) FROM basic_price WHERE deleted_at IS NULL) > 0 THEN 'SUCCESS'
        WHEN (SELECT COUNT(*) FROM qualification WHERE deleted_at IS NULL) > 0 THEN 'PARTIAL - Qualification only'
        WHEN (SELECT COUNT(*) FROM basic_price WHERE deleted_at IS NULL) > 0 THEN 'PARTIAL - Basic price only'
        ELSE 'FAILED - No records found'
    END as migration_status;
